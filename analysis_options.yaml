include: package:flutter_lints/flutter.yaml

linter:
  rules:
    # Const improvements
    prefer_const_constructors: true
    prefer_const_constructors_in_immutables: true
    prefer_const_declarations: true
    prefer_const_literals_to_create_immutables: true

    # Style & Clean Code
    always_declare_return_types: true
    avoid_print: true
    prefer_final_locals: true
    prefer_final_in_for_each: true
    unnecessary_this: true
    sort_constructors_first: true
    type_annotate_public_apis: true
    avoid_unnecessary_containers: true

    # Formatting
    sort_pub_dependencies: true
    require_trailing_commas: true
    curly_braces_in_flow_control_structures: true
    directives_ordering: true

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.gr.dart"
    - "**/*.config.dart"

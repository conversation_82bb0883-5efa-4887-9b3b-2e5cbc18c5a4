import 'package:afa_app/models/home_screens_models/sessions_q&a_models/answers_model.dart';

class QuestionsModel {
  QuestionsModel.fromJson(Map<String, dynamic> jsonData) {
    question = jsonData['question'];
    userName = jsonData['user'];
    userImage = jsonData.containsKey('avatar') ? jsonData['avatar'] : null;
    answers = (jsonData['answers'] as List)
        .map((answer) => AnswersModel.fromJson(answer))
        .toList();
  }
  String? question, userName, userImage;
  List<AnswersModel>? answers;
}

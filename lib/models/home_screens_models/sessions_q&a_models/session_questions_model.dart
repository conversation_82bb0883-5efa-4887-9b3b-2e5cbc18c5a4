import 'package:afa_app/models/home_screens_models/sessions_q&a_models/question_and_answers_speakers_model.dart';
import 'package:afa_app/models/home_screens_models/sessions_q&a_models/question_model.dart';

class SessionQuestionsModel {
  SessionQuestionsModel.fromJson(Map<String, dynamic> jsonData) {
    sesionID = jsonData['id'];
    title = jsonData['session_title'];
    startTime = jsonData['session_start_time'];
    endTime = jsonData['session_end_time'];
    questions = (jsonData['questions'] as List)
        .map((qustion) => QuestionsModel.fromJson(qustion))
        .toList();

    speakers = (jsonData['speakers'] as List)
        .map((speaker) => QandASpeakersModel.fromJson(speaker))
        .toList();
  }
  int? sesionID;
  String? title, startTime, endTime;
  List<QuestionsModel>? questions;

  List<QandASpeakersModel>? speakers;
}

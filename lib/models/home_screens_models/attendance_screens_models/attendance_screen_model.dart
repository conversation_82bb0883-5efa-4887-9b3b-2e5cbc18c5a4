import 'package:afa_app/models/home_screens_models/attendance_screens_models/attendance_experience_model.dart';
import 'package:afa_app/models/home_screens_models/attendance_screens_models/attendance_social_links_model.dart';

class AttendanceScreenModel {
  AttendanceScreenModel.fromJson(Map<String, dynamic> jsonData) {
    attendanceID = jsonData['id'];
    userName = jsonData['full_name'] ?? "null";
    userImage = jsonData['profile_image'];
    jobName = jsonData['job_title'] ?? "null";
    company = jsonData['company'] ?? "null";
    country = jsonData['country'] ?? "null";
    categorySelected = jsonData['category_select'] ?? "null";
    biography = jsonData['biography'] ?? "null";
    speakingAt = jsonData['speaking_at'] ?? "null";
    socialLinks = (jsonData['social_links'] as List)
        .map((links) => AttendanceSocialLinksModel.fromJson(links))
        .toList();
    experiences = (jsonData['experience'] as List)
        .map((experiences) => AttendanceExperienceModel.fromJson(experiences))
        .toList();
  }

  String? attendanceID,
      userName,
      userImage,
      jobName,
      company,
      country,
      categorySelected,
      biography,
      speakingAt;
  List<AttendanceSocialLinksModel>? socialLinks;
  List<AttendanceExperienceModel>? experiences;
}

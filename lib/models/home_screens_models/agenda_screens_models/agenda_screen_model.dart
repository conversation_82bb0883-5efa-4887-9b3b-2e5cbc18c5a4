import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_details_speakers_model.dart';

class AgendaScreenModel {
  AgendaScreenModel.fromJson(Map<String, dynamic> jsonData) {
    agendaID = jsonData['id'];
    sessionTitle = jsonData['session_title'];
    sessionStartTime = jsonData['session_start_time'];
    sessionEndTime = jsonData['session_end_time'];
    sessionDate = (jsonData['time_period'] as List).isEmpty
        ? ""
        : jsonData['time_period'][0];

    speakersList = (jsonData['speakers'] as List)
        .map((speaker) => AgendaDetailsSpeakersModel.fromJson(speaker))
        .toList();
  }
  int? agendaID;
  String? sessionTitle, sessionStartTime, sessionEndTime, sessionDate;
  List<AgendaDetailsSpeakersModel>? speakersList;
}

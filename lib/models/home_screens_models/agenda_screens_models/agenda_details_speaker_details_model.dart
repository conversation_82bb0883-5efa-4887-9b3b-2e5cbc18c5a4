import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_social_media_links_model.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_speakers_experience_model.dart';

class AgendaDetailsSpeakerDetailsModel {
  AgendaDetailsSpeakerDetailsModel.fromJson(Map<String, dynamic> jsonData) {
    speakerName = jsonData['full_name'];
    speakerImage = jsonData['profile_image'];
    categorySelect = jsonData['category_select'];
    job = jsonData['job_title'];
    company = jsonData['company'];
    country = jsonData['country'];
    about = jsonData['biography'];
    speakingAt = jsonData['speaking_at'];
    socialMediaLinks = (jsonData['social_links'] as List)
        .map((links) => AgendaSocialMediaLinksModel.fromJson(links))
        .toList();

    experience = (jsonData['experience'] as List)
        .map((experience) => AgendaSpeakersExperienceModel.from<PERSON><PERSON>(experience))
        .toList();
  }
  String? speakerName,
      speakerImage,
      categorySelect,
      job,
      company,
      country,
      about,
      speakingAt;
  List<AgendaSocialMediaLinksModel>? socialMediaLinks;
  List<AgendaSpeakersExperienceModel>? experience;
}

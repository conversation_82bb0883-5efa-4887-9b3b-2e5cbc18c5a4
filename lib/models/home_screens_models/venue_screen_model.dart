class VenueScreenModel {
  VenueScreenModel.fromJson(Map<String, dynamic> jsonData) {
    title = jsonData['name'];
    description = jsonData['description'];
    featuredImage = jsonData['featured_image'];
    images = jsonData['images_gallery'];
    locationLat = jsonData['location_on_google_maps']['lat'];
    locationLong = jsonData['location_on_google_maps']['lng'];
  }
  String? title, description, featuredImage;
  double? locationLat, locationLong;
  List<dynamic>? images;
}

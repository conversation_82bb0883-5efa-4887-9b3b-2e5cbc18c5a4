import 'package:afa_app/models/home_screens_models/speakers_screens_models/speaker_social_links_model.dart';
import 'package:afa_app/models/home_screens_models/speakers_screens_models/speakers_experience_model.dart';

class SpeakerScreenModel {
  SpeakerScreenModel.fromJson(Map<String, dynamic> jsonData) {
    speakerID = jsonData['id'];
    name = jsonData['full_name'] ?? "";
    image = jsonData['profile_image'];
    jobTitle = jsonData['job_title'] ?? "";
    companyName = jsonData['company'] ?? "";
    country = jsonData['country'] ?? "";
    category = jsonData['category_select'] ?? "";
    about = jsonData['biography'] ?? "";
    speakingAt = (jsonData['speaking_at'] is List)
        ? "null"
        : jsonData['speaking_at'];

    socialMediaLinks = (jsonData['social_links'] as List)
        .map((links) => SpeakerSocialLinksModel.fromJson(links))
        .toList();

    experiences = (jsonData['experience'] as List)
        .map((experience) => SpeakersExperienceModel.fromJson(experience))
        .toList();
  }
  String? speakerID,
      name,
      image,
      companyName,
      country,
      category,
      jobTitle,
      about;
  dynamic speakingAt;

  List<SpeakerSocialLinksModel>? socialMediaLinks;
  List<SpeakersExperienceModel>? experiences;
}

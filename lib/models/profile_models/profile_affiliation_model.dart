import 'package:afa_app/models/profile_models/profile_companies_model.dart';

class ProfileAffiliationModel {
  ProfileAffiliationModel.fromJson(Map<String, dynamic> jsonData) {
    company = ProfileCompaniesModel(
      companyId: jsonData['company_name']['ID'],
      companyName: jsonData['company_name']['post_title'],
    );
    title = jsonData['title'];
    startdate = jsonData['start_date'];
    endDate = jsonData['end_date'];
  }

  ProfileAffiliationModel({
    this.company,
    this.title,
    this.startdate,
    this.endDate,
  });

  Map<String, dynamic> toJson() => {
    "company_name": company!.companyId,
    "title": title,
    "start_date": startdate,
    "end_date": endDate,
    "current": true,
  };

  ProfileCompaniesModel? company;
  String? title, startdate, endDate;
}

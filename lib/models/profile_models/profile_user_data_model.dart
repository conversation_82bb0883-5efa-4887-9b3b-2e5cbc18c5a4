class ProfileUserDataModel {
  ProfileUserDataModel.fromJson(Map<String, dynamic> jsonData) {
    userID = jsonData['id'];
    userName = jsonData['name'];
    userImage = jsonData['avatar'];
    userEmail = jsonData['email'];
    country = jsonData['country'] ?? "Egypt";
    position = jsonData['position'];
    bio = jsonData['bio'];
    phoneNumber = jsonData['phone'];
  }
  int? userID;
  dynamic userName, userImage, userEmail, country, position, bio, phoneNumber;
}

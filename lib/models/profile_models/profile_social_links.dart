class ProfileSocialLinksModel {
  ProfileSocialLinksModel.fromJson(Map<String, dynamic> jsonData) {
    socialMediaName = jsonData['social_media_name'];
    socialMediaUrl = jsonData['social_media_link'];
  }

  ProfileSocialLinksModel({this.socialMediaName, this.socialMediaUrl});

  String? socialMediaName, socialMediaUrl;

  Map<String, dynamic> toJson() => {
    "social_media_name": socialMediaName,
    "social_media_link": socialMediaUrl,
  };
}

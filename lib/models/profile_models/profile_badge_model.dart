class ProfileBadgeModel {
  ProfileBadgeModel({
    this.eventTitle,
    this.eventDate,
    this.company,
    this.eventLocation,
    this.supportEmail,
    this.qrCode,
    this.badgeUsage,
  });
  ProfileBadgeModel.fromJson(Map<String, dynamic> jsonData) {
    eventTitle = jsonData['event']['title'];
    eventLocation = jsonData['event']['location'];
    eventDate = jsonData['event']['date'];
    company = jsonData['company'];
    country = jsonData['country'];
    qrCode = jsonData['qr_code'];
    supportEmail = jsonData['support_email'];
    badgeUsage = jsonData['badge_usage'];
  }
  String? eventTitle,
      eventLocation,
      eventDate,
      company,
      country,
      qrCode,
      supportEmail;
  List<dynamic>? badgeUsage;
}

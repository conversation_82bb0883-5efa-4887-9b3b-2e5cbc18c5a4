class ProfileEducationModel {
  ProfileEducationModel.fromJson(Map<String, dynamic> jsonData) {
    schoolName = jsonData['school_name'];
    degree = jsonData['degree'];
    major = jsonData['major'];
    schoolStartDate = jsonData['school_start_date'];
    schoolEndDate = jsonData['school_end_date'];
  }

  ProfileEducationModel({
    this.schoolName,
    this.degree,
    this.major,
    this.schoolStartDate,
    this.schoolEndDate,
  });
  String? schoolName, degree, major, schoolStartDate, schoolEndDate;

  Map<String, dynamic> toJson() => {
    "school_name": schoolName,
    "degree": degree,
    "major": major,
    "school_start_date": schoolStartDate,
    "school_end_date": schoolEndDate,
  };
}

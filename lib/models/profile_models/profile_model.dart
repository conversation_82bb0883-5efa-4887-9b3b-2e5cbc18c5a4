import 'package:afa_app/models/profile_models/profile_affiliation_model.dart';
import 'package:afa_app/models/profile_models/profile_links_model.dart';
import 'package:afa_app/models/profile_models/profile_social_links.dart';
import 'package:afa_app/models/profile_models/profille_education_model.dart';

class ProfileModel {
  ProfileModel.fromJson(Map<String, dynamic> jsonData) {
    userAffiliations = (jsonData['affiliation'] as List)
        .map((affiliations) => ProfileAffiliationModel.fromJson(affiliations))
        .toList();

    userEduactions = (jsonData['education'] as List)
        .map((education) => ProfileEducationModel.fromJson(education))
        .toList();

    userSocialLinks = (jsonData['social_media_links'] as List)
        .map((socialLinks) => ProfileSocialLinksModel.fromJson(socialLinks))
        .toList();

    userLinks = (jsonData['links'] as List)
        .map((links) => ProfileLinksModel.fromJson(links))
        .toList();
  }
  List<ProfileAffiliationModel>? userAffiliations;
  List<ProfileEducationModel>? userEduactions;
  List<ProfileSocialLinksModel>? userSocialLinks;
  List<ProfileLinksModel>? userLinks;
}

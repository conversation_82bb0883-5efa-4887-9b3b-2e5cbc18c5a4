class RequestMeetingModel {
  RequestMeetingModel({
    this.userID,
    this.title,
    this.description,
    this.location,
    this.date,
    this.time,
  });
  int? userID;
  String? title, description, location, date, time;

  Map<String, dynamic> toJson() => {
    "recipient_id": userID,
    "title": title,
    "description": description,
    "location": location,
    "date": date,
    "time": time,
  };
}

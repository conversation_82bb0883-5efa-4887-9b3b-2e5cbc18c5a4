import 'package:afa_app/models/polls_screen_models/poll_options_model.dart';

class PollsQuestionsModel {
  PollsQuestionsModel.fromJson(Map<String, dynamic> jsonData) {
    userId = jsonData['created_by'] != null ? jsonData['created_by']['id'] : 0;
    userName = jsonData['created_by'] != null
        ? jsonData['created_by']['name']
        : "";
    userImage = jsonData['created_by'] != null
        ? jsonData['created_by']['avatar']
        : "";
    pollID = jsonData['id'];
    question = jsonData['title'];
    pollsOption = (jsonData['choices'] as List)
        .map((options) => PollOptionsModel.fromJson(options))
        .toList();
  }

  PollsQuestionsModel({
    this.question,
    this.pollsOption,
    this.pollID,
    this.userName,
    this.userId,
    this.userImage,
  });

  int? pollID, userId;
  String? userName, userImage, question;
  List<PollOptionsModel>? pollsOption;

  Map<String, dynamic> toJson() => {
    "title": question,
    "question": question,
    "choices": pollsOption!
        .map(
          (options) => {
            "label": options.optionTitle,
            "uid": options.optionID,
            "votes": options.votesCount,
          },
        )
        .toList(),
  };
}

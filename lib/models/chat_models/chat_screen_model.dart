class ChatScreenModel {
  ChatScreenModel.fromJson(
    Map<String, dynamic> jsonData, {
    required String name,
    required String image,
  }) {
    senderID = jsonData['sender_id'];
    message = jsonData['message'];
    userName = name;
    userImage = image;
  }

  ChatScreenModel({this.message, this.userName, this.userImage, this.senderID});
  int? senderID;
  String? message, userName, userImage;
}

import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/models/home_screens_models/speakers_screens_models/speeaker_screen_model.dart';
import 'package:flutter/material.dart';

class SpeakersScreenProvidersApis extends ChangeNotifier {
  Future<List<SpeakerScreenModel>> getSpeakersList({
    required BuildContext context,
  }) async {
    final List<SpeakerScreenModel> speakersList = [];

    final List<dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/speakers",
      headers: {},
    );

    if (dataList != null) {
      for (final data in dataList) {
        speakersList.add(SpeakerScreenModel.fromJson(data));
      }
    } else {
      debugPrint("ERROR WITH getSpeakersList FUNCTION");
    }

    return speakersList;
  }
}

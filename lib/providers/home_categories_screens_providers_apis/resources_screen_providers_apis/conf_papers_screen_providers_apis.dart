import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/models/home_screens_models/resources_screens_models/conf_papers_screen_model.dart';
import 'package:flutter/material.dart';

class ConfPapersScreenProvidersApis extends ChangeNotifier {
  Future<List<ConfPapersScreenModel>> getAllConfPapers({
    required BuildContext context,
  }) async {
    final List<ConfPapersScreenModel> confPapers = [];
    final List<dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/bp-ext/v1/conf-papers",
      headers: {},
    );

    if (dataList != null) {
      for (final data in dataList) {
        confPapers.add(ConfPapersScreenModel.fromJson(data));
      }
    } else {
      debugPrint("ERROR WITH getAllConfPapers FUNCTION");
    }

    return confPapers;
  }
}

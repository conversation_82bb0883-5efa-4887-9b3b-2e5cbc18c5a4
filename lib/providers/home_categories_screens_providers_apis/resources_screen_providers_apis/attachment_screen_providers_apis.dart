import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/models/home_screens_models/resources_screens_models/attachment_screen_model.dart';
import 'package:flutter/material.dart';

class AttachmentScreenProvidersApis extends ChangeNotifier {
  Future<List<AttachmentScreenModel>> getAttachmentsList({
    required BuildContext context,
  }) async {
    final List<AttachmentScreenModel> attachmentsList = [];
    final Map<String, dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/documents",
      headers: {},
    );

    if (dataList != null) {
      for (final data in dataList['items']) {
        attachmentsList.add(AttachmentScreenModel.fromJson(data));
      }
    } else {
      debugPrint("ERROR WITH getAttachmentsList FUNCTION");
    }

    return attachmentsList;
  }
}

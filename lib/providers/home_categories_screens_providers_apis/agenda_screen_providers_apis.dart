import 'dart:math';
import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_days_model.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_details_speaker_details_model.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_screen_model.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_sessions_model.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_speakers_model.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_time_slots_model.dart';
import 'package:flutter/material.dart';

class AgendaScreenProvidersApis extends ChangeNotifier {
  AgendaDaysModel? getAgendaByTimePeriod;
  AgendaTimeSlotsModel? getAgendaByTimeSlot;
  AgendaSpeakersModel? getAgendaBySpeaker;
  AgendaSessionsModel? getAgendaBySession;

  Key? agendaKey;

  void selectTimePeriod(AgendaDaysModel? daysModel) {
    getAgendaByTimePeriod = daysModel;
    notifyListeners();
  }

  void selectTimeSlots(AgendaTimeSlotsModel? timeSlot) {
    getAgendaByTimeSlot = timeSlot;
    notifyListeners();
  }

  void selectSpeaker(AgendaSpeakersModel? speaker) {
    getAgendaBySpeaker = speaker;
    notifyListeners();
  }

  void selectSession(AgendaSessionsModel? session) {
    getAgendaBySession = session;
    notifyListeners();
  }

  void rebuildAgendaWidget() {
    agendaKey = ValueKey(Random().nextInt(10000));
    notifyListeners();
  }

  Future<List<AgendaScreenModel>> getAgendaList({
    required BuildContext context,
    bool setInitialTimePeriod = true,
    bool isSerach = false,
    String? searchText,
  }) async {
    final List<AgendaScreenModel> agendaList = [];
    if (setInitialTimePeriod) {
      final List<AgendaDaysModel> daysList =
          await getAgendaFiltersList(context: context, getTimePeriodsList: true)
              as List<AgendaDaysModel>;

      selectTimePeriod(daysList[0]);
    }

    if (!context.mounted) return [];
    final List<dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl:
          setInitialTimePeriod == true &&
              getAgendaByTimeSlot == null &&
              getAgendaBySpeaker == null &&
              getAgendaBySession == null
          ? "wp-json/buddypress-ext/v1/agenda/filter?time_period=${getAgendaByTimePeriod!.dayName}"
          : isSerach
          ? "wp-json/buddypress-ext/v1/agenda/search?q=$searchText"
          : "wp-json/buddypress-ext/v1/agenda/filter?session=${getAgendaBySession?.sessionName ?? ""}&speaker_name=${getAgendaBySpeaker?.speakerName ?? ""}&time_slots=${getAgendaByTimeSlot?.timeSlots ?? ""}&time_period=${getAgendaByTimePeriod?.dayName ?? ""}",
      headers: {},
    );

    if (dataList != null) {
      for (final data in dataList) {
        agendaList.add(AgendaScreenModel.fromJson(data));
      }
    } else {
      debugPrint("ERROR WITH getAgendaList FUNCTION");
    }

    return agendaList;
  }

  Future<List<dynamic>> getAgendaFiltersList({
    required BuildContext context,
    bool getTimePeriodsList = false,
    bool getTimeSlotsList = false,
    bool getSpeakersList = false,
    bool getSessionList = false,
  }) async {
    final List<AgendaDaysModel> daysList = [];
    final List<AgendaSpeakersModel> speakersList = [];
    final List<AgendaSessionsModel> sessionsList = [];
    final List<AgendaTimeSlotsModel> timeSlotsList = [];

    final Map<String, dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/agenda/filters-list",
      headers: {},
    );

    if (dataList != null) {
      if (getTimePeriodsList) {
        for (final data in dataList['time_periods']) {
          daysList.add(AgendaDaysModel.fromJson(data));
        }
      } else if (getSpeakersList) {
        for (final data in dataList['speakers']) {
          speakersList.add(AgendaSpeakersModel.fromJson(data));
        }
      } else if (getSessionList) {
        for (final data in dataList['sessions']) {
          sessionsList.add(AgendaSessionsModel.fromJson(data));
        }
      } else if (getTimeSlotsList) {
        for (final data in dataList['time_slots']) {
          timeSlotsList.add(AgendaTimeSlotsModel.fromJson(data));
        }
      }
    } else {
      debugPrint("ERROR WITH getAgendaFiltersList FUNCTION");
    }
    if (getTimePeriodsList) {
      // selectTimePeriod(daysList[0]);
      return daysList;
    } else if (getTimeSlotsList) {
      return timeSlotsList;
    } else if (getSessionList) {
      return sessionsList;
    } else {
      return speakersList;
    }
  }

  Future<AgendaDetailsSpeakerDetailsModel> getSpeakerDetailsFromAgenda({
    required BuildContext context,
    required int speakerID,
  }) async {
    AgendaDetailsSpeakerDetailsModel? speakerDetails;

    final Map<String, dynamic>? data = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/speakers/$speakerID",
      headers: {},
    );

    if (data != null) {
      speakerDetails = AgendaDetailsSpeakerDetailsModel.fromJson(data);
    } else {
      debugPrint("ERROR WITH getSpeakerDetailsFromAgenda FUNCTION");
    }

    return speakerDetails!;
  }

  // Future<void> sendRequestMeeting({
  //   required BuildContext context,
  //   required int agendaID,
  //   required String title,
  //   required String description,
  //   required String location,
  //   required String date,
  //   required String time,
  // }) async {
  //   final AgendaRequestMeetingModel model = AgendaRequestMeetingModel(
  //     title: title,
  //     description: description,
  //     location: location,
  //     date: date,
  //     time: time,
  //   );
  //   final String myToken = await CommonComponents.getSavedData(
  //     ApiKeys.userToken,
  //   );
  //   if (!context.mounted) return;
  //   final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
  //     context: context,
  //     baseUrl: ApiKeys.baseUrl,
  //     apiUrl: "wp-json/buddypress-ext/v1/agenda/$agendaID/request-meeting",
  //     headers: {
  //       "Content-Type": "application/json",
  //       "Authorization": "Bearer $myToken",
  //     },
  //     body: jsonEncode(model.toJson()),
  //   );

  //   if (data != null) {
  //     if (!context.mounted) return;
  //     if (data.containsKey('status')) {
  //       CommonComponents.showCustomizedSnackBar(
  //         context: context,
  //         title: "Your Request Sent Successfully",
  //       );
  //     } else {
  //       CommonComponents.showCustomizedSnackBar(
  //         context: context,
  //         title: data['message'],
  //       );
  //     }
  //   } else {
  //     debugPrint("ERROR WITH sendRequestMeeting FUNCTION");
  //   }
  // }
}

import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/profile_models/profile_photos_model.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ProfilePhotosProviderApis extends ChangeNotifier {
  int page = 1;
  Key? photosKey;

  void incrementPage() {
    page++;
    notifyListeners();
  }

  void rebuildPhotosWidget() {
    photosKey = ValueKey(Random().nextInt(1000));
    notifyListeners();
  }

  void addBoundaryKey() {
    boundarykeys.add(GlobalKey());
    notifyListeners();
  }

  List<ProfilePhotosModel> photosList = [];
  List<GlobalKey> boundarykeys = [];

  Future<List<ProfilePhotosModel>> getProfilePhotos({
    required BuildContext context,
    required RefreshController controller,
    bool initialLoading = false,
    bool isRefresh = false,
    bool isLoading = false,
  }) async {
    if (isRefresh || initialLoading) {
      page = 1;
      photosList = [];
      boundarykeys = [];
      controller.resetNoData();
    } else if (isLoading) {
      incrementPage();
    }

    final Map<String, dynamic>? dataList = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/conf-photos/paginated",
      headers: {},
      body: {"page": page.toString(), "per_page": "10"},
      showLoadingWidget: false,
    );
    if (dataList != null) {
      if (page <= dataList['total_pages']) {
        for (final data in dataList['items']) {
          photosList.add(ProfilePhotosModel.fromJson(data));
          addBoundaryKey();
        }

        if (isRefresh) {
          controller.refreshCompleted();
        } else if (isLoading) {
          controller.loadComplete();
        }
      } else {
        controller.loadNoData();
      }
    } else {
      debugPrint("ERROR WITH getProfilePhotos FUNCTION");
    }

    return photosList;
  }

  Future<void> uploadPhotoToServer({
    required BuildContext context,
    required File? photo,
  }) async {
    try {
      final int userID = await CommonComponents.getSavedData(ApiKeys.userID);
      final myToken = await CommonComponents.getSavedData(ApiKeys.userToken);

      if (await CommonComponents.checkConnectivity()) {
        if (context.mounted) {
          CommonComponents.loading(context);
        } else {
          return;
        }

        const String url =
            "${ApiKeys.baseUrl}wp-json/buddypress-ext/v1/conf-photos";

        final http.MultipartRequest response = http.MultipartRequest(
          "POST",
          Uri.parse(url),
        );

        // response.headers["Content-Type"] = "application/json";
        response.headers["Authorization"] = "Bearer $myToken";

        if (photo != null) {
          response.files.add(
            await http.MultipartFile.fromPath('conf_photo', photo.path),
          );
        }

        response.files.addAll([
          http.MultipartFile.fromString('uploaded_by', userID.toString()),
          http.MultipartFile.fromString(
            'photo_date',
            DateFormat().add_yMMMMd().format(DateTime.now()).toString(),
          ),
        ]);

        await response.send().then((results) async {
          await http.Response.fromStream(results).then((response) async {
            if (response.statusCode == 200) {
              // final decoddedData = jsonDecode(response.body);
              if (!context.mounted) return;
              Navigator.pop(context);
              CommonComponents.showCustomizedSnackBar(
                context: context,
                title: "Image Uploaded Successfully",
              );
            } else {
              if (!context.mounted) return;
              CommonComponents.showCustomizedSnackBar(
                context: context,
                title: "Server Error Request",
              );
              Navigator.pop(context);
            }
          });
        });
      } else {
        if (context.mounted) {
          Navigator.pop(context);
          await CommonComponents.notConnectionAlert(context);
        } else {
          return;
        }
      }
    } on TimeoutException catch (error) {
      if (context.mounted) {
        Navigator.pop(context);
      }
      debugPrint("Time Out Exception is::=>$error");
      if (context.mounted) {
        await CommonComponents.timeOutExceptionAlert(context);
      }
    } on SocketException catch (error) {
      if (context.mounted) {
        Navigator.pop(context);
      }
      debugPrint("Socket Exception is::=>$error");
      if (context.mounted) {
        await CommonComponents.socketExceptionAlert(context);
      }
    } catch (error) {
      if (context.mounted) {
        Navigator.pop(context);
      }
      debugPrint("General Exception is::=>$error");
    }
  }
}

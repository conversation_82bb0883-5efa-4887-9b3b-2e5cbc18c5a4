import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/profile_models/profile_badge_model.dart';
import 'package:flutter/material.dart';

class ProfileBadgeProvidersApis extends ChangeNotifier {
  Future<ProfileBadgeModel> getProfileBadge({
    required BuildContext context,
  }) async {
    ProfileBadgeModel badge = ProfileBadgeModel();
    final int userID = await CommonComponents.getSavedData(ApiKeys.userID);

    if (!context.mounted) return badge;

    final Map<String, dynamic>? data = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/badge/$userID",
      headers: {},
    );

    if (data != null) {
      badge = ProfileBadgeModel.fromJson(data);
    } else {
      debugPrint("ERROR WITH getProfileBadge FUNCTION");
    }

    return badge;
  }
}

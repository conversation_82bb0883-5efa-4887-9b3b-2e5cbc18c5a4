import 'dart:convert';
import 'dart:math';

import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/profile_models/profile_affiliation_model.dart';
import 'package:afa_app/models/profile_models/profile_companies_model.dart';
import 'package:afa_app/models/profile_models/profile_links_model.dart';
import 'package:afa_app/models/profile_models/profile_model.dart';
import 'package:afa_app/models/profile_models/profile_social_links.dart';
import 'package:afa_app/models/profile_models/profille_education_model.dart';
import 'package:flutter/material.dart';

class ProfilePersonalInformationProvidersApis extends ChangeNotifier {
  Key? profileKey;
  ProfileCompaniesModel? companySelected;

  void rebuildProfileData() {
    profileKey = ValueKey(Random().nextInt(1000));
    notifyListeners();
  }

  void setCompanySelected(ProfileCompaniesModel? company) {
    companySelected = company;
    notifyListeners();
  }

  Future<ProfileModel> getProfileData({required BuildContext context}) async {
    ProfileModel? profileData;
    final int userId = await CommonComponents.getSavedData(ApiKeys.userID);

    if (!context.mounted) return profileData!;

    final Map<String, dynamic>? data = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/profile/$userId",
      headers: {},
    );

    if (data != null) {
      profileData = ProfileModel.fromJson(data);
    } else {
      debugPrint("ERROR WITH getProfileData FUNCTION");
    }

    return profileData!;
  }

  Future<List<ProfileCompaniesModel>> getAllCompanies({
    required BuildContext context,
  }) async {
    final List<ProfileCompaniesModel> companiesList = [];

    final List<dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/companies",
      headers: {},
    );

    if (dataList != null) {
      for (final data in dataList) {
        companiesList.add(ProfileCompaniesModel.fromJson(data));
      }
    } else {
      debugPrint("ERROR WITH getAllCompanies FUNCTION");
    }

    return companiesList;
  }

  Future<void> userCreateAffiliation({
    required BuildContext context,
    required String title,
    required String startDate,
    required String endaDate,
  }) async {
    final int userId = await CommonComponents.getSavedData(ApiKeys.userID);
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );

    final ProfileAffiliationModel affiliationModel = ProfileAffiliationModel(
      company: companySelected!,
      title: title,
      startdate: startDate,
      endDate: endaDate,
    );
    if (!context.mounted) return;
    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/profile/$userId/affiliation",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $myToken",
      },
      body: json.encode(affiliationModel.toJson()),
    );

    if (data != null) {
      if (!context.mounted) return;
      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: "Your Affiliation Added Successfully",
      );
    } else {
      debugPrint("ERROR WITH userCreateAffiliation FUNCTION");
    }
  }

  Future<void> userEditAffiliation({
    required BuildContext context,
    required String title,
    required String startDate,
    required String endaDate,
    required int index,
  }) async {
    final int userId = await CommonComponents.getSavedData(ApiKeys.userID);
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );

    final ProfileAffiliationModel affiliationModel = ProfileAffiliationModel(
      company: companySelected!,
      title: title,
      startdate: startDate,
      endDate: endaDate,
    );
    if (!context.mounted) return;
    final Map<String, dynamic>? data = await ApiRequests.putApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/profile/$userId/affiliation/$index",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $myToken",
      },
      body: json.encode(affiliationModel.toJson()),
    );

    if (data != null) {
      if (!context.mounted) return;
      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: "Your Affiliation Changed Successfully",
      );
    } else {
      debugPrint("ERROR WITH userEditAffiliation FUNCTION");
    }
  }

  Future<void> deleteUserAffilation({
    required BuildContext context,
    required int index,
  }) async {
    final int userId = await CommonComponents.getSavedData(ApiKeys.userID);
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );

    if (!context.mounted) return;
    final Map<String, dynamic>? data = await ApiRequests.deleteApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/profile/$userId/affiliation/$index",
      headers: {"Authorization": "Bearer $myToken"},
    );

    if (data != null) {
      if (!context.mounted) return;
      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: "Your Affiliation Deleted Successfully",
      );
    } else {
      debugPrint("ERROR WITH deleteUserAffilation FUNCTION");
    }
  }

  Future<void> userCreateEducation({
    required BuildContext context,
    required String schoolName,
    required String degree,
    required String major,
    required String startDate,
    required String endDate,
  }) async {
    final int userId = await CommonComponents.getSavedData(ApiKeys.userID);
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );
    final ProfileEducationModel profileEducation = ProfileEducationModel(
      schoolName: schoolName,
      degree: degree,
      major: major,
      schoolStartDate: startDate,
      schoolEndDate: endDate,
    );

    if (!context.mounted) return;

    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/profile/$userId/education",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $myToken",
      },
      body: json.encode(profileEducation.toJson()),
    );

    if (data != null) {
      if (!context.mounted) return;
      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: "Your Eduaction Created Successfully",
      );
    } else {
      debugPrint("ERROR WITH userCreateEducation FUNCTION");
    }
  }

  Future<void> userEditEducation({
    required BuildContext context,
    required String schoolName,
    required String degree,
    required String major,
    required String startDate,
    required String endDate,
    required int index,
  }) async {
    final int userId = await CommonComponents.getSavedData(ApiKeys.userID);
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );
    final ProfileEducationModel profileEducation = ProfileEducationModel(
      schoolName: schoolName,
      degree: degree,
      major: major,
      schoolStartDate: startDate,
      schoolEndDate: endDate,
    );

    if (!context.mounted) return;

    final Map<String, dynamic>? data = await ApiRequests.putApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/profile/$userId/education/$index",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $myToken",
      },
      body: json.encode(profileEducation.toJson()),
    );

    if (data != null) {
      if (!context.mounted) return;
      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: "Your Eduaction Changed Successfully",
      );
    } else {
      debugPrint("ERROR WITH userEditEducation FUNCTION");
    }
  }

  Future<void> deleteUserEducation({
    required BuildContext context,
    required int index,
  }) async {
    final int userId = await CommonComponents.getSavedData(ApiKeys.userID);
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );

    if (!context.mounted) return;

    final Map<String, dynamic>? data = await ApiRequests.deleteApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/profile/$userId/education/$index",
      headers: {"Authorization": "Bearer $myToken"},
    );

    if (data != null) {
      if (!context.mounted) return;

      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: "Your Education Deleted Successfully",
      );
    } else {
      debugPrint("ERROR WITH deleteUserEducation FUNCTION");
    }
  }

  Future<void> addSocialMediaLink({
    required BuildContext context,
    required String linkName,
    required String linkurl,
  }) async {
    final ProfileSocialLinksModel socialMediaLinkModel =
        ProfileSocialLinksModel(
          socialMediaName: linkName,
          socialMediaUrl: linkurl,
        );

    final int userId = await CommonComponents.getSavedData(ApiKeys.userID);
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );
    if (!context.mounted) return;

    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/profile/$userId/social_media_links",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $myToken",
      },
      body: json.encode(socialMediaLinkModel.toJson()),
    );

    if (data != null) {
      if (!context.mounted) return;
      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: "Your Social Media Link Added Successfully",
      );
    } else {
      debugPrint("ERROR WITH addSocialMediaLink FUNCTION");
    }
  }

  Future<void> editSocialMediaLink({
    required BuildContext context,
    required String linkName,
    required String linkurl,
    required int index,
  }) async {
    final ProfileSocialLinksModel socialMediaLinkModel =
        ProfileSocialLinksModel(
          socialMediaName: linkName,
          socialMediaUrl: linkurl,
        );

    final int userId = await CommonComponents.getSavedData(ApiKeys.userID);
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );
    if (!context.mounted) return;

    final Map<String, dynamic>? data = await ApiRequests.putApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl:
          "wp-json/buddypress-ext/v1/profile/$userId/social_media_links/$index",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $myToken",
      },
      body: json.encode(socialMediaLinkModel.toJson()),
    );

    if (data != null) {
      if (!context.mounted) return;
      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: "Your Social Media Link Changed Successfully",
      );
    } else {
      debugPrint("ERROR WITH editSocialMediaLink FUNCTION");
    }
  }

  Future<void> deleteSocialMediaLink({
    required BuildContext context,
    required int index,
  }) async {
    final int userId = await CommonComponents.getSavedData(ApiKeys.userID);
    final myToken = await CommonComponents.getSavedData(ApiKeys.userToken);

    if (!context.mounted) return;

    final Map<String, dynamic>? data = await ApiRequests.deleteApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl:
          "wp-json/buddypress-ext/v1/profile/$userId/social_media_links/$index",
      headers: {"Authorization": "Bearer $myToken"},
    );

    if (data != null) {
      if (!context.mounted) return;
      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: "Your Social Media Link Deleted Successfully",
      );
    } else {
      debugPrint("ERROR WITH deleteSocialMediaLink FUNCTION");
    }
  }

  Future<void> addUserLink({
    required BuildContext context,
    required String linkName,
    required String linkUrl,
  }) async {
    final int userId = await CommonComponents.getSavedData(ApiKeys.userID);
    final myToken = await CommonComponents.getSavedData(ApiKeys.userToken);

    if (!context.mounted) return;

    final ProfileLinksModel linksModel = ProfileLinksModel(
      linkName: linkName,
      linkUrl: linkUrl,
    );

    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/profile/$userId/links",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $myToken",
      },
      body: json.encode(linksModel.toJson()),
    );

    if (data != null) {
      if (!context.mounted) return;

      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: "Your Link Added Successfuly",
      );
    } else {
      debugPrint("ERROR WITH addUserLink FUNCTION");
    }
  }

  Future<void> editUserLink({
    required BuildContext context,
    required String linkName,
    required String linkUrl,
    required int index,
  }) async {
    final int userId = await CommonComponents.getSavedData(ApiKeys.userID);
    final myToken = await CommonComponents.getSavedData(ApiKeys.userToken);

    if (!context.mounted) return;

    final ProfileLinksModel linksModel = ProfileLinksModel(
      linkName: linkName,
      linkUrl: linkUrl,
    );

    final Map<String, dynamic>? data = await ApiRequests.putApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/profile/$userId/links/$index",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $myToken",
      },
      body: json.encode(linksModel.toJson()),
    );

    if (data != null) {
      if (!context.mounted) return;

      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: "Your Link Changed Successfuly",
      );
    } else {
      debugPrint("ERROR WITH editUserLink FUNCTION");
    }
  }

  Future<void> deleteUserLink({
    required BuildContext context,
    required int index,
  }) async {
    final int userId = await CommonComponents.getSavedData(ApiKeys.userID);
    final myToken = await CommonComponents.getSavedData(ApiKeys.userToken);

    if (!context.mounted) return;

    final Map<String, dynamic>? data = await ApiRequests.deleteApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/profile/$userId/links/$index",
      headers: {"Authorization": "Bearer $myToken"},
    );

    if (data != null) {
      if (!context.mounted) return;
      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: "Your Link Deleted Successfully",
      );
    } else {
      debugPrint("ERROR WITH deleteUserLink FUNCTION");
    }
  }
}

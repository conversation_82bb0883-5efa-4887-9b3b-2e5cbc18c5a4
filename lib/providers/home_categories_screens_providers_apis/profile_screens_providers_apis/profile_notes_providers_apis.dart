import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/profile_models/profile_notes_model.dart';
import 'package:flutter/material.dart';

class ProfileNotesProvidersApis extends ChangeNotifier {
  Future<List<ProfileNotesModel>> getProfileNotes({
    required BuildContext context,
  }) async {
    final List<ProfileNotesModel> notesList = [];
    final myToken = await CommonComponents.getSavedData(ApiKeys.userToken);

    if (!context.mounted) return [];

    final List<dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/notes",
      headers: {"Authorization": "Bearer $myToken"},
    );

    if (dataList != null) {
      for (final data in dataList) {
        notesList.add(ProfileNotesModel.fromJson(data));
      }
    } else {
      debugPrint("ERROR WITH getProfileNotes FUNCTION");
    }
    return notesList;
  }
}

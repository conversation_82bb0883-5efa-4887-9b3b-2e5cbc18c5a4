import 'dart:convert';
import 'dart:math';
import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/polls_screen_models/poll_options_model.dart';
import 'package:afa_app/models/polls_screen_models/polls_questions_model.dart';
import 'package:flutter/material.dart';

class PollsScreenProvidersApis extends ChangeNotifier {
  Key? pollsKey;

  void rebuildPollsWidget() {
    pollsKey = ValueKey(Random().nextInt(1000));
    notifyListeners();
  }

  Future<List<PollsQuestionsModel>> getAllPolls({
    required BuildContext context,
  }) async {
    final List<PollsQuestionsModel> pollsList = [];

    final List<dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/polls/",
      headers: {},
    );

    if (dataList != null) {
      for (final data in dataList) {
        pollsList.add(PollsQuestionsModel.fromJson(data));
      }
    } else {
      debugPrint("ERROR WITH getAllPolls FUNCTION");
    }
    return pollsList;
  }

  Future<void> userMakeVote({
    required BuildContext context,
    required int pollID,
    required String optionID,
  }) async {
    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/polls/$pollID/vote",
      headers: {},
      body: {"option": optionID.toString()},
      showLoadingWidget: false,
    );

    if (data != null) {
      if (!context.mounted) return;
      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: "Your Vote Sent Successfully",
      );
    } else {
      debugPrint("ERROR WITH userMakeVote FUNCTION");
    }
  }

  Future<void> userCreatepoll({
    required BuildContext context,
    required String question,
    required List<PollOptionsModel> optionsList,
  }) async {
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );

    final PollsQuestionsModel pollsQuestionsModel = PollsQuestionsModel(
      question: question,
      pollsOption: optionsList,
    );
    if (!context.mounted) return;
    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/polls",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $myToken",
      },
      body: json.encode(pollsQuestionsModel.toJson()),
    );
    if (data != null) {
      if (!context.mounted) return;
      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: "Your Poll Created Successfully",
      );
    } else {
      debugPrint("ERROR WITH userCreatepoll FUNCTION");
    }
  }
}

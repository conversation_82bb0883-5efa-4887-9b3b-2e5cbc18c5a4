import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/models/announcements_screen_model.dart';
import 'package:flutter/material.dart';

class AnnouncementsScreenProvidersApis extends ChangeNotifier {
  Future<List<AnnouncementsScreenModel>> getAnnouncements({
    required BuildContext context,
  }) async {
    final List<AnnouncementsScreenModel> announcemntsList = [];

    final List<dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/announcementt",
      headers: {},
    );

    if (dataList != null) {
      for (final data in dataList) {
        announcemntsList.add(AnnouncementsScreenModel.fomJson(data));
      }
    } else {
      debugPrint("ERROR WITH getAnnouncements FUNCTION");
    }

    return announcemntsList;
  }
}

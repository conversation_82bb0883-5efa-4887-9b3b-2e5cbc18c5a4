import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/models/home_screens_models/sponsors_screen_models/sponsor_screen_model.dart';
import 'package:flutter/material.dart';

class SponsorsScreenProvidersApis extends ChangeNotifier {
  Future<List<SponsorScreenModel>> getSponsorsList({
    required BuildContext context,
  }) async {
    final List<SponsorScreenModel> sponsorsList = [];

    final List<dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/sponsors-by-category",
      headers: {},
    );

    if (dataList != null) {
      for (final data in dataList) {
        sponsorsList.add(SponsorScreenModel.fromJson(data));
      }
    } else {
      debugPrint("ERROR WITH getSponsorsList FUNCTION");
    }

    return sponsorsList;
  }
}

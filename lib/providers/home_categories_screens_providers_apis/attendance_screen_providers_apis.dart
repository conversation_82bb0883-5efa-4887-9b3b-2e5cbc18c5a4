import 'dart:math';
import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/home_screens_models/attendance_screens_models/attendance_categories_model.dart';
import 'package:afa_app/models/home_screens_models/attendance_screens_models/attendance_screen_model.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class AttendanceScreenProvidersApis extends ChangeNotifier {
  AttendanceCategoriesModel? categorySelected;
  int page = 1;

  // Key? attendanceKey;

  void incrementPage() {
    page++;
    notifyListeners();
  }

  void setCategorySelected(AttendanceCategoriesModel category) {
    categorySelected = category;
    notifyListeners();
  }

  // void rebuildAttendanceWidget() {
  //   attendanceKey = ValueKey(Random().nextInt(10000));
  //   // notifyListeners();
  // }

  List<AttendanceScreenModel> attendancesList = [];

  Future<List<AttendanceScreenModel>> getAttendanceList({
    required BuildContext context,
    required RefreshController controller,
    bool initialLoading = false,
    bool isRefresh = false,
    bool isLoading = false,
    bool showLoading = false,
  }) async {
    if (showLoading) {
      CommonComponents.loading(context);
    }

    if (isRefresh || initialLoading) {
      page = 1;
      attendancesList = [];
      controller.resetNoData();
    } else if (isLoading) {
      incrementPage();
    }

    final Map<String, dynamic>? dataList = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/attendancee/paginated",
      body: categorySelected != null
          ? {
              "page": page.toString(),
              "per_page": "15",
              "category_select": categorySelected!.id.toString(),
            }
          : {"page": page.toString(), "per_page": "15"},
      headers: {},
      showLoadingWidget: false,
    );

    if (dataList != null) {
      if (page <= dataList['total_pages']) {
        for (final data in dataList['items']) {
          attendancesList.add(AttendanceScreenModel.fromJson(data));
        }
        if (isRefresh) {
          controller.refreshCompleted();
        } else if (isLoading) {
          controller.loadComplete();
        }
      } else {
        controller.loadNoData();
      }
    } else {
      debugPrint("ERROR WITH getAttendanceList FUNCTION");
    }
    if (showLoading) {
      if (!context.mounted) return [];
      Navigator.pop(context);
    }
    return attendancesList;
  }

  Color getRandomColor() {
    final Random random = Random();
    return Color.fromARGB(
      255,
      random.nextInt(256),
      random.nextInt(256),
      random.nextInt(256),
    );
  }

  Future<List<AttendanceCategoriesModel>> getAttendanceCategories({
    required BuildContext context,
  }) async {
    final List<AttendanceCategoriesModel> categoriesList = [];

    final List<dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/attendancee/types",
      headers: {},
    );

    if (dataList != null) {
      for (final data in dataList) {
        categoriesList.add(
          AttendanceCategoriesModel.fromJson(data, getRandomColor()),
        );
      }
    } else {
      debugPrint("ERROR WITH getAttendanceCategories FUNCTION");
    }

    return categoriesList;
  }
}

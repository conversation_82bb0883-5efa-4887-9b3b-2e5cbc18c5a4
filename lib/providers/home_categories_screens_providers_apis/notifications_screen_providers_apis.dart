import 'dart:convert';

import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/notifications_model.dart';
import 'package:flutter/material.dart';

class NotificationsScreenProvidersApis extends ChangeNotifier {
  int unreadNotificationCount = 0;

  void addUnreadNotificationCount() {
    unreadNotificationCount++;
    notifyListeners();
  }

  void setUnreadNotificationCount(int count) {
    unreadNotificationCount = count;
    notifyListeners();
  }

  Future<List<NotificationsModel>> getAllNotifications({
    required BuildContext context,
    bool showBadgeWithHome = false,
  }) async {
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );

    final List<NotificationsModel> notictaionsList = [];

    if (!context.mounted) return [];
    final Map<String, dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/notifications",
      headers: {"Authorization": "Bearer $myToken"},
    );

    if (dataList != null) {
      for (final data in dataList['items']) {
        notictaionsList.add(NotificationsModel.fromJson(data));
        if (showBadgeWithHome) {
          if (!data['read']) {
            addUnreadNotificationCount();
          }
        }
      }
    } else {
      debugPrint("ERROR WITH getAllNotifications FUNCTION");
    }
    return notictaionsList;
  }

  Future<void> readNotifiation({required BuildContext context}) async {
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );

    final int userID = await CommonComponents.getSavedData(ApiKeys.userID);
    if (!context.mounted) return;
    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/notifications/read",
      headers: {
        "Authorization": "Bearer $myToken",
        "Content-Type": "application/json",
      },
      body: json.encode({"id": userID}),
      showLoadingWidget: false,
    );

    if (data != null) {
      setUnreadNotificationCount(0);
    } else {
      debugPrint("ERROR WITH readNotifiation FUNCTION");
    }
  }
}

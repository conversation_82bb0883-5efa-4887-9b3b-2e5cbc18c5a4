import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/models/home_screens_models/exibitors_screen_model.dart';
import 'package:flutter/material.dart';

class ExibitorsScreenProvidersApis extends ChangeNotifier {
  Future<List<ExibitorsScreenModel>> getExibitorsList({
    required BuildContext context,
  }) async {
    final List<ExibitorsScreenModel> exibitorsList = [];
    final Map<String, dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/exhibitors",
      headers: {},
    );

    if (dataList != null) {
      for (final data in dataList['items']) {
        exibitorsList.add(ExibitorsScreenModel.fromJson(data));
      }
    } else {
      debugPrint("ERROR WITH getExibitorsList FUNCTION");
    }

    return exibitorsList;
  }
}

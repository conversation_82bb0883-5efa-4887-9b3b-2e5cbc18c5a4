import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/models/home_screens_models/venue_screen_model.dart';
import 'package:flutter/material.dart';

class VenueScreenProvidersApis extends ChangeNotifier {
  Future<List<VenueScreenModel>> getVenueDetails({
    required BuildContext context,
  }) async {
    final List<VenueScreenModel> venueData = [];

    final List<dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/bp-ext/v1/venues",
      headers: {},
    );

    if (dataList != null) {
      for (final data in dataList) {
        venueData.add(VenueScreenModel.fromJson(data));
      }
    } else {
      debugPrint("ERROR WITH getVenueDetails FUNCTION");
    }

    return venueData;
  }
}

import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_providers/profile_providers.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/chat_models/inbox_model.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_context/riverpod_context.dart';

class InboxProvidersApis extends ChangeNotifier {
  int? threadID;

  void setThreadID(int id) {
    threadID = id;
    notifyListeners();
  }

  Future<List<InboxModel>> getChatInbox({required BuildContext context}) async {
    final List<InboxModel> inboxList = [];
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );

    final int myUserId = await CommonComponents.getSavedData(ApiKeys.userID);

    if (!context.mounted) return [];
    final Map<String, dynamic>? dataList = await ApiRequests.getApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/better-messages/v1/threads",
      headers: {"Authorization": "Bearer $myToken"},
    );

    if (dataList != null) {
      for (final data in dataList['threads']) {
        if (data['participantsCount'] == 2) {
          if (!context.mounted) return [];

          for (final participants in data['participants']) {
            if (participants != myUserId) {
              await context
                  .read(ProfileProviders.profileSettingsProvidersApis)
                  .getUserData(
                    context: context,
                    getMyProfileData: false,
                    userID: participants,
                  )
                  .then((value) {
                    inboxList.add(
                      InboxModel.fromJson(
                        data,
                        userName: value.userName!,
                        userImage: value.userImage!,
                      ),
                    );
                  });
            }
          }
        }
      }
    } else {
      debugPrint("ERROR WITH getChatInbox FUNCTION");
    }

    return inboxList;
  }
}

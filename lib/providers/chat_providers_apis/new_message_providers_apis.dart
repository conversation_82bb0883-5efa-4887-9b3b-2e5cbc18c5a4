import 'dart:convert';

import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_providers/chat_providers.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_context/riverpod_context.dart';

class NewMessageProvidersApis extends ChangeNotifier {
  Future<void> sendNewMessage({
    required BuildContext context,
    required int recipientId,
    required String message,
  }) async {
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );

    if (!context.mounted) return;

    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/better-messages/v1/thread/new",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $myToken",
      },
      body: json.encode({
        "recipients": [recipientId.toString()],
        "message": message,
        "subject": message,
      }),
    );

    if (data != null) {
      if (!context.mounted) return;
      context
          .read(ChatProviders.inboxProvidersApis)
          .setThreadID(data['thread_id']);
      Navigator.pop(context);
      Navigator.pushNamed(context, PATHS.chatScreen);
    } else {
      debugPrint("ERROR WITH NewMessageProvidersApis FUNCTION");
    }
  }
}

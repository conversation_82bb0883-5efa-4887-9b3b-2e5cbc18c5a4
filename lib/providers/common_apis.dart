import 'dart:convert';
import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/request_meeting_model.dart';
import 'package:afa_app/models/take_note_model.dart';
import 'package:flutter/material.dart';

class CommonApis extends ChangeNotifier {
  int currentIndexForMainScreen = 0;

  void setCurrentIndex(int index) {
    currentIndexForMainScreen = index;
    notifyListeners();
  }

  Future<void> sendNote({
    required BuildContext context,
    required String note,
  }) async {
    final TakeNoteModel model = TakeNoteModel(note: note);
    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );

    if (!context.mounted) return;
    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/notes",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $myToken",
      },
      body: jsonEncode(model.toJson()),
    );

    if (data != null) {
      if (data.containsKey('success')) {
        if (!context.mounted) return;
        CommonComponents.showCustomizedSnackBar(
          context: context,
          title: "Note Sent Successfully",
        );
        Navigator.pop(context);
      } else {
        if (!context.mounted) return;
        CommonComponents.showCustomizedSnackBar(
          context: context,
          title: data['message'],
        );
      }
    } else {
      debugPrint("ERROR WITH sendNote FUNCTION");
    }
  }

  Future<void> sendRequestMeeting({
    required BuildContext context,
    required String title,
    required String descriptipn,
    required String location,
    required String date,
    required String time,
  }) async {
    final int userId = await CommonComponents.getSavedData(ApiKeys.userID);

    final RequestMeetingModel model = RequestMeetingModel(
      userID: userId,
      title: title,
      description: descriptipn,
      location: location,
      date: date,
      time: time,
    );

    final String myToken = await CommonComponents.getSavedData(
      ApiKeys.userToken,
    );
    if (!context.mounted) return;

    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/attendancee/meeting-request",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $myToken",
      },
      body: jsonEncode(model.toJson()),
    );

    if (data != null) {
      if (!context.mounted) return;
      if (data.containsKey('status')) {
        CommonComponents.showCustomizedSnackBar(
          context: context,
          title: "Your Request Sent Successfully",
        );
      } else {
        CommonComponents.showCustomizedSnackBar(
          context: context,
          title: data['message'],
        );
      }
    } else {
      debugPrint("ERROR WITH sendRequestMeeting FUNCTION");
    }
  }
}

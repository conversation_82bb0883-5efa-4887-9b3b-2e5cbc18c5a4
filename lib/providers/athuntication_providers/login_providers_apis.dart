import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_requests.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:afa_app/models/athuntication_models/login_model.dart';
import 'package:flutter/material.dart';

class LoginProvidersApis extends ChangeNotifier {
  Future<void> userLogin({
    required BuildContext context,
    required String email,
    required String password,
    // required bool userSelectedRememberMe,
    required String fcmToken,
  }) async {
    final LoginModel model = LoginModel(email: email, password: password);

    final Map<String, dynamic>? data = await ApiRequests.postApiRequest(
      context: context,
      baseUrl: ApiKeys.baseUrl,
      apiUrl: "wp-json/buddypress-ext/v1/auth/login",
      headers: {},
      body: model.toJson(fcmToken: fcmToken),
    );

    if (data != null) {
      if (data.containsKey('token')) {
        // if (userSelectedRememberMe) {
        await CommonComponents.saveData(
          key: ApiKeys.userToken,
          value: data['token'],
        );
        // }

        await CommonComponents.saveData(
          key: ApiKeys.userID,
          value: data['user_id'],
        );

        await CommonComponents.saveData(
          key: ApiKeys.userName,
          value: data['name'],
        );

        await CommonComponents.saveData(
          key: ApiKeys.userPosition,
          value: data['position'] ?? "No Position",
        );

        await CommonComponents.saveData(
          key: ApiKeys.userImage,
          value: data['avatar'] ?? CommonComponents.imageNotFound,
        );

        if (!context.mounted) return;
        Navigator.pushNamedAndRemoveUntil(
          context,
          PATHS.mainScreen,
          (route) => false,
        );
      } else {
        if (!context.mounted) return;
        await CommonComponents.showCustomizedAlert(
          context: context,
          title: data['code'],
          subTitle: data['message'],
        );
      }
    } else {
      if (!context.mounted) return;
      await CommonComponents.showCustomizedAlert(
        context: context,
        title: "Login Method",
        subTitle:
            "Email/user name Or Password is incorrect ,please Check Your Email/user name and Password and try again",
      );
    }
  }

  // Future<void> getUserId({
  //   required BuildContext context,
  //   required String token,
  // }) async {
  //   final Map<String, dynamic>? data = await ApiRequests.getApiRequest(
  //     context: context,
  //     baseUrl: ApiKeys.baseUrl,
  //     apiUrl: "wp-json/buddypress-ext/v1/auth/me",
  //     headers: {"Authorization": "Bearer $token"},
  //   );

  //   if (data != null) {
  //     await CommonComponents.saveData(key: ApiKeys.userID, value: data['id']);
  //   } else {
  //     debugPrint("ERROR WITH getUserId FUNCTION");
  //   }
  // }
}

import 'package:afa_app/screens/athuntication_screens/login_screen.dart';
import 'package:afa_app/screens/athuntication_screens/otp_screen.dart';
import 'package:afa_app/screens/athuntication_screens/reset_password_screen.dart';
import 'package:afa_app/screens/home_screens/chat_screen.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/accomodation_screen.dart';
// import 'package:afa_app/screens/athuntication_screens/set_new_password_screen.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/agenda_screen.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/agenda_screens/agenda_details_screen.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/agenda_screens/agenda_details_speaker_screen.dart';
// import 'package:afa_app/screens/home_screens/home_categories_screens/agenda_screens/request_meeting_screen_agenda.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/attendance_screen.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/attendance_screens/attendance_details_screen.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/attendance_screens/request_meeting_screen_attendance.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/exibitors_screen.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/resources_screen.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/resources_screens/about_event_screen.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/resources_screens/attachment_screen.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/resources_screens/conf_papers_screen.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/resources_screens/resources_poll_screen.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/session_q&a_details_screens/questions_screen.dart';

import 'package:afa_app/screens/home_screens/home_categories_screens/session_questions_screen.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/speakers_screen.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/speakers_screens/request_meeting_screen_speaker.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/speakers_screens/speaker_details_screen.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/sponsors_screen.dart';

import 'package:afa_app/screens/home_screens/main_screen.dart';
import 'package:afa_app/screens/home_screens/profile_screen.dart';
import 'package:afa_app/screens/home_screens/profile_screens/badge_screen.dart';
import 'package:afa_app/screens/home_screens/profile_screens/change_password_screen.dart';
import 'package:afa_app/screens/home_screens/profile_screens/notes_screen.dart';
import 'package:afa_app/screens/home_screens/profile_screens/personal_information_screen.dart';
import 'package:afa_app/screens/home_screens/profile_screens/profile_photos_screen.dart';
import 'package:afa_app/screens/home_screens/profile_screens/setting_screen.dart';
import 'package:afa_app/screens/notification_screen.dart';
import 'package:afa_app/splash_screen.dart';
import 'package:flutter/material.dart';

class PATHS {
  static const String splashScreen = "SplashScreen";
  static const String loginScreen = "LoginScreen";
  static const String resetPasswordScreen = "ResetPasswordScreen";
  static const String otpScreen = "OtpScreen";
  // static const String setNewPasswordScreen = "SetNewPasswordScreen";
  static const String mainScreen = "MainScreen";
  static const String agendaScreen = "AgendaScreen";
  static const String attendanceScreen = "AttendanceScreen";
  static const String speakersScreen = "SpeakersScreen";
  static const String exibitorsScreen = "ExibitorsScreen";
  static const String sponsorsScreen = "SponsorsScreen";
  static const String accomodationScreen = "AccomodationScreen";
  static const String aboutEventScreen = "AboutEventScreen";
  static const String resourcesScreen = "ResourcesScreen";
  static const String attendanceDetailsScreen = "AttendanceDetailsScreen";
  static const String requestMeetingScreenAttendance =
      "RequestMeetingScreenAttendance";
  static const String agendaDetailsScreen = "AgendaDetailsScreen";
  static const String profileScreen = "ProfileScreen";
  static const String settingScreen = "SettingScreen";
  static const String personalInformationScreen = "PersonalInformationScreen";
  static const String changePasswordScreen = "ChangePasswordScreen";
  static const String badgeScreen = "BadgeScreen";
  static const String speakerDetailsScreen = "SpeakerDetailsScreen";
  static const String requestMeetingScreenSpeaker =
      "RequestMeetingScreenSpeaker";
  static const String sessionQuestionsScreen = "SessionQuestionsScreen";
  static const String attachmentScreen = "AttachmentScreen";

  static const String agendaDetailsSpeakerScreen = "AgendaDetailsSpeakerScreen";
  static const String confPapersScreen = "ConfPapersScreen";
  static const String notesScreen = "NotesScreen";
  static const String photosScreen = "PhotosScreen";
  static const String notificationScreen = "NotificationScreen";
  static const String resourcesPollScreen = "ResourcesPollScreen";
  static const String chatScreen = "ChatScreen";
  static const String questionScreen = "QuestionScreen";
  // static const String requestMeetingScreenAgenda = "RequestMeetingScreenAgenda";
}

Map<String, StatefulWidget Function(BuildContext context)> routes = {
  PATHS.splashScreen: (context) => const SplashScreen(),
  PATHS.loginScreen: (context) => const LoginScreen(),
  PATHS.resetPasswordScreen: (context) => const ResetPasswordScreen(),
  PATHS.otpScreen: (context) => const OtpScreen(),
  // PATHS.setNewPasswordScreen: (context) => const SetNewPasswordScreen(),
  PATHS.mainScreen: (context) => const MainScreen(),
  PATHS.agendaScreen: (context) => const AgendaScreen(),
  PATHS.attendanceScreen: (context) => const AttendanceScreen(),
  PATHS.speakersScreen: (context) => const SpeakersScreen(),
  PATHS.exibitorsScreen: (context) => const ExibitorsScreen(),
  PATHS.sponsorsScreen: (context) => const SponsorsScreen(),
  PATHS.accomodationScreen: (context) => const AccomodationScreen(),
  PATHS.aboutEventScreen: (context) => const AboutEventScreen(),
  PATHS.resourcesScreen: (context) => const ResourcesScreen(),
  PATHS.attendanceDetailsScreen: (context) => const AttendanceDetailsScreen(),
  PATHS.requestMeetingScreenAttendance: (context) =>
      const RequestMeetingScreenAttendance(),
  PATHS.agendaDetailsScreen: (context) => const AgendaDetailsScreen(),
  PATHS.profileScreen: (context) => const ProfileScreen(),
  PATHS.settingScreen: (context) => const SettingScreen(),
  PATHS.personalInformationScreen: (context) =>
      const PersonalInformationScreen(),
  PATHS.changePasswordScreen: (context) => const ChangePasswordScreen(),
  PATHS.badgeScreen: (context) => const BadgeScreen(),
  PATHS.speakerDetailsScreen: (context) => const SpeakerDetailsScreen(),
  PATHS.requestMeetingScreenSpeaker: (context) =>
      const RequestMeetingScreenSpeaker(),

  PATHS.sessionQuestionsScreen: (context) => const SessionQuestionsScreen(),
  PATHS.attachmentScreen: (context) => const AttachmentScreen(),

  PATHS.agendaDetailsSpeakerScreen: (context) =>
      const AgendaDetailsSpeakerScreen(),

  PATHS.confPapersScreen: (context) => const ConfPapersScreen(),
  PATHS.notesScreen: (context) => const NotesScreen(),
  PATHS.photosScreen: (context) => const ProfilePhotosScreen(),
  PATHS.notificationScreen: (context) => const NotificationScreen(),
  PATHS.resourcesPollScreen: (context) => const ResourcesPollScreen(),
  PATHS.chatScreen: (context) => const ChatScreen(),
  PATHS.questionScreen: (context) => const QuestionsScreen(),

  // PATHS.requestMeetingScreenAgenda: (context) =>
  //     const RequestMeetingScreenAgenda(),
};

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:afa_app/app_config/common_components.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class ApiRequests {
  static Future<dynamic> getApiRequest({
    required BuildContext context,
    required String baseUrl,
    required String apiUrl,
    required Map<String, String> headers,
  }) async {
    try {
      if (await CommonComponents.checkConnectivity()) {
        final String url = "$baseUrl$apiUrl";

        final http.Response response = await http.get(
          Uri.parse(url),
          headers: {}..addAll(headers),
        );

        if (response.statusCode == 200) {
          final succesDecodedData = jsonDecode(response.body);
          return succesDecodedData;
        } else if (response.statusCode == 500) {
          if (!context.mounted) return;
          await CommonComponents.showCustomizedAlert(
            context: context,
            title: "Server Error",
            subTitle: "internal_server_error",
          );
          final failedDecodedData = jsonDecode(response.body);
          return failedDecodedData;
        } else {
          debugPrint("ERROR WITH $apiUrl STATUSCODE !=200");
          final failedDecodedData = jsonDecode(response.body);
          return failedDecodedData;
        }
      } else {
        if (context.mounted) {
          await CommonComponents.notConnectionAlert(context);
        }
      }
    } on TimeoutException catch (error) {
      if (context.mounted) {
        debugPrint("Time Out Exception is::=>$error with api=>$apiUrl");
        if (!context.mounted) return;
        await CommonComponents.timeOutExceptionAlert(context);
      }
    } on SocketException catch (error) {
      debugPrint("Socket Exception is::=>$error with api=>$apiUrl");
      if (!context.mounted) return;
      await CommonComponents.socketExceptionAlert(context);
    } catch (error) {
      debugPrint("General Exception is::=>$error with api=>$apiUrl");
    }
  }

  static Future<dynamic> postApiRequest({
    required BuildContext context,
    required String baseUrl,
    required String apiUrl,
    required Map<String, String> headers,
    required dynamic body,
    bool showLoadingWidget = true,
  }) async {
    try {
      if (await CommonComponents.checkConnectivity()) {
        if (!context.mounted) return;
        if (showLoadingWidget) CommonComponents.loading(context);

        final String url = "$baseUrl$apiUrl";

        final http.Response response = await http.post(
          Uri.parse(url),
          headers: {}..addAll(headers),
          body: body,
        );

        if (response.statusCode == 200 || response.statusCode == 201) {
          if (!context.mounted) return;
          if (showLoadingWidget) Navigator.pop(context);
          final successDecodedData = jsonDecode(response.body);
          return successDecodedData;
        } else if (response.statusCode == 500) {
          if (!context.mounted) return;
          await CommonComponents.showCustomizedAlert(
            context: context,
            title: "Server Error",
            subTitle: "internal_server_error",
          );
          final failedDecodedData = jsonDecode(response.body);
          return failedDecodedData;
        } else {
          debugPrint(
            "POST METHOD=> status Code !=200 or 201 with api=>$apiUrl",
          );
          if (!context.mounted) return;
          if (showLoadingWidget) Navigator.pop(context);
          final failedDecodedData = jsonDecode(response.body);
          return failedDecodedData;
        }
      } else {
        if (!context.mounted) return;
        await CommonComponents.notConnectionAlert(context);
      }
    } on TimeoutException catch (error) {
      debugPrint("Time Out Exception is::=>$error with api=>$apiUrl");

      if (!context.mounted) return;
      Navigator.pop(context);
      await CommonComponents.timeOutExceptionAlert(context);
    } on SocketException catch (error) {
      debugPrint("Socket Exception is::=>$error with api=>$apiUrl");
      if (!context.mounted) return;
      Navigator.pop(context);
      await CommonComponents.socketExceptionAlert(context);
    } catch (error) {
      if (!context.mounted) return;
      Navigator.pop(context);
      debugPrint("General Exception is::=>$error with api=>$apiUrl");
    }
  }

  static Future<dynamic> putApiRequest({
    required BuildContext context,
    required String baseUrl,
    required String apiUrl,
    required Map<String, String> headers,
    required dynamic body,
    bool showLoadingWidget = true,
  }) async {
    try {
      if (await CommonComponents.checkConnectivity()) {
        if (!context.mounted) return;
        if (showLoadingWidget) CommonComponents.loading(context);

        final String url = "$baseUrl$apiUrl";
        final http.Response response = await http.put(
          Uri.parse(url),
          headers: {}..addAll(headers),
          body: body,
        );

        if (response.statusCode == 200 || response.statusCode == 201) {
          if (!context.mounted) return;
          if (showLoadingWidget) Navigator.pop(context);
          final succeededDecodedData = jsonDecode(response.body);

          return succeededDecodedData;
        } else if (response.statusCode == 500) {
          if (!context.mounted) return;
          await CommonComponents.showCustomizedAlert(
            context: context,
            title: "Server Error",
            subTitle: "internal_server_error",
          );
          final failedDecodedData = jsonDecode(response.body);
          return failedDecodedData;
        } else {
          debugPrint("PUT METHOD status Code !=200 or 201 with api=>$apiUrl");
          if (!context.mounted) return;
          if (showLoadingWidget) Navigator.pop(context);
          final failedDecodedData = jsonDecode(response.body);
          return failedDecodedData;
        }
      } else {
        if (!context.mounted) return;
        await CommonComponents.notConnectionAlert(context);
      }
    } on TimeoutException catch (error) {
      debugPrint("Time Out Exception is::=>$error with api=>$apiUrl");

      if (!context.mounted) return;
      Navigator.pop(context);
      await CommonComponents.timeOutExceptionAlert(context);
    } on SocketException catch (error) {
      debugPrint("Socket Exception is::=>$error with api=>$apiUrl");
      if (!context.mounted) return;
      Navigator.pop(context);
      await CommonComponents.socketExceptionAlert(context);
    } catch (error) {
      if (!context.mounted) return;
      Navigator.pop(context);
      debugPrint("General Exception is::=>$error with api=>$apiUrl");
    }
  }

  static Future<dynamic> deleteApiRequest({
    required BuildContext context,
    required String baseUrl,
    required String apiUrl,
    required Map<String, String> headers,
    bool showLoadingWidget = true,
  }) async {
    try {
      if (await CommonComponents.checkConnectivity()) {
        if (!context.mounted) return;
        if (showLoadingWidget) CommonComponents.loading(context);

        final String url = "$baseUrl$apiUrl";
        final http.Response response = await http.delete(
          Uri.parse(url),
          headers: {}..addAll(headers),
        );

        if (response.statusCode == 200 || response.statusCode == 201) {
          if (!context.mounted) return;
          if (showLoadingWidget) Navigator.pop(context);
          final suceededDecodedData = jsonDecode(response.body);
          return suceededDecodedData;
        } else if (response.statusCode == 500) {
          if (!context.mounted) return;
          await CommonComponents.showCustomizedAlert(
            context: context,
            title: "Server Error",
            subTitle: "internal_server_error",
          );
          final failedDecodedData = jsonDecode(response.body);
          return failedDecodedData;
        } else {
          debugPrint(
            "DELETE METHOD status Code !=200 or 201 with api=>$apiUrl",
          );
          if (!context.mounted) return;
          if (showLoadingWidget) Navigator.pop(context);
          final failedDecodedData = jsonDecode(response.body);
          return failedDecodedData;
        }
      } else {
        if (!context.mounted) return;
        await CommonComponents.notConnectionAlert(context);
      }
    } on TimeoutException catch (error) {
      debugPrint("Time Out Exception is::=>$error with api=>$apiUrl");

      if (!context.mounted) return;
      Navigator.pop(context);
      await CommonComponents.timeOutExceptionAlert(context);
    } on SocketException catch (error) {
      debugPrint("Socket Exception is::=>$error with api=>$apiUrl");
      if (!context.mounted) return;
      Navigator.pop(context);
      await CommonComponents.socketExceptionAlert(context);
    } catch (error) {
      if (!context.mounted) return;
      Navigator.pop(context);
      debugPrint("General Exception is::=>$error with api=>$apiUrl");
    }
  }
}

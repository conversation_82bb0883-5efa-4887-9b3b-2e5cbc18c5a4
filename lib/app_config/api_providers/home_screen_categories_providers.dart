import 'package:afa_app/providers/home_categories_screens_providers_apis/agenda_screen_providers_apis.dart';
import 'package:afa_app/providers/home_categories_screens_providers_apis/announcements_screen_providers_apis.dart';
import 'package:afa_app/providers/home_categories_screens_providers_apis/attendance_screen_providers_apis.dart';
import 'package:afa_app/providers/home_categories_screens_providers_apis/exibitors_screen_providers_apis.dart';
import 'package:afa_app/providers/home_categories_screens_providers_apis/notifications_screen_providers_apis.dart';
import 'package:afa_app/providers/home_categories_screens_providers_apis/polls_screen_providers_apis.dart';
import 'package:afa_app/providers/home_categories_screens_providers_apis/question_and_answers_providers_apis.dart';
import 'package:afa_app/providers/home_categories_screens_providers_apis/resources_screen_providers_apis/attachment_screen_providers_apis.dart';
import 'package:afa_app/providers/home_categories_screens_providers_apis/resources_screen_providers_apis/conf_papers_screen_providers_apis.dart';
import 'package:afa_app/providers/home_categories_screens_providers_apis/speakers_screen_providers_apis.dart';
import 'package:afa_app/providers/home_categories_screens_providers_apis/sponsors_screen_providers_apis.dart';
import 'package:afa_app/providers/home_categories_screens_providers_apis/venue_screen_providers_apis.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class HomeScreenCategoriesProviders {
  static final ChangeNotifierProvider<AttendanceScreenProvidersApis>
  attendanceScreenProvidersApis = ChangeNotifierProvider(
    (ref) => AttendanceScreenProvidersApis(),
  );

  static final ChangeNotifierProvider<PollsScreenProvidersApis>
  pollsScreenProvidersApis = ChangeNotifierProvider(
    (ref) => PollsScreenProvidersApis(),
  );

  static final ChangeNotifierProvider<AgendaScreenProvidersApis>
  agendaScreenProvidersApis = ChangeNotifierProvider(
    (ref) => AgendaScreenProvidersApis(),
  );

  static final ChangeNotifierProvider<SponsorsScreenProvidersApis>
  sponsorsScreenProvidersApis = ChangeNotifierProvider(
    (ref) => SponsorsScreenProvidersApis(),
  );

  static final ChangeNotifierProvider<ExibitorsScreenProvidersApis>
  exibitorsScreenProvidersApis = ChangeNotifierProvider(
    (ref) => ExibitorsScreenProvidersApis(),
  );

  static final ChangeNotifierProvider<AttachmentScreenProvidersApis>
  attachmentScreenProvidersApis = ChangeNotifierProvider(
    (ref) => AttachmentScreenProvidersApis(),
  );

  static final ChangeNotifierProvider<AnnouncementsScreenProvidersApis>
  announcementsScreenProvidersApis = ChangeNotifierProvider(
    (ref) => AnnouncementsScreenProvidersApis(),
  );

  static final ChangeNotifierProvider<QuestionAndAnswersProvidersApis>
  questionAndAnswersProvidersApis = ChangeNotifierProvider(
    (ref) => QuestionAndAnswersProvidersApis(),
  );

  static final ChangeNotifierProvider<SpeakersScreenProvidersApis>
  speakersScreenProvidersApis = ChangeNotifierProvider(
    (ref) => SpeakersScreenProvidersApis(),
  );

  static final ChangeNotifierProvider<ConfPapersScreenProvidersApis>
  confPapersScreenProvidersApis = ChangeNotifierProvider(
    (ref) => ConfPapersScreenProvidersApis(),
  );

  static final ChangeNotifierProvider<VenueScreenProvidersApis>
  venueScreenProvidersApis = ChangeNotifierProvider(
    (ref) => VenueScreenProvidersApis(),
  );

  static final ChangeNotifierProvider<NotificationsScreenProvidersApis>
  notificationsScreenProvidersApis = ChangeNotifierProvider(
    (ref) => NotificationsScreenProvidersApis(),
  );
}

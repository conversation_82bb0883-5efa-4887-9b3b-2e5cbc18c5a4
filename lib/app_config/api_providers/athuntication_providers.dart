import 'package:afa_app/providers/athuntication_providers/login_providers_apis.dart';
import 'package:afa_app/providers/athuntication_providers/reset_password_providers_apis.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class AthunticationProviders {
  static final ChangeNotifierProvider<LoginProvidersApis> loginProvidersApis =
      ChangeNotifierProvider((ref) => LoginProvidersApis());

  static final ChangeNotifierProvider<ResetPasswordProvidersApis>
  resetPasswordProvidersApis = ChangeNotifierProvider(
    (ref) => ResetPasswordProvidersApis(),
  );
}

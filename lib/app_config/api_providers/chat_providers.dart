import 'package:afa_app/providers/chat_providers_apis/chat_providers_apis.dart';
import 'package:afa_app/providers/chat_providers_apis/inbox_providers_apis.dart';
import 'package:afa_app/providers/chat_providers_apis/new_message_providers_apis.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ChatProviders {
  static final ChangeNotifierProvider<InboxProvidersApis> inboxProvidersApis =
      ChangeNotifierProvider((ref) => InboxProvidersApis());

  static final ChangeNotifierProvider<ChatProvidersApis> chatProvidersApis =
      ChangeNotifierProvider((ref) => ChatProvidersApis());

  static final ChangeNotifierProvider<NewMessageProvidersApis>
  newMessageProvidersApis = ChangeNotifierProvider(
    (ref) => NewMessageProvidersApis(),
  );
}

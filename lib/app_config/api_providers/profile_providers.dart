import 'package:afa_app/providers/common_apis.dart';
import 'package:afa_app/providers/home_categories_screens_providers_apis/profile_screens_providers_apis/profile_badge_providers_apis.dart';
import 'package:afa_app/providers/home_categories_screens_providers_apis/profile_screens_providers_apis/profile_notes_providers_apis.dart';
import 'package:afa_app/providers/home_categories_screens_providers_apis/profile_screens_providers_apis/profile_personal_information_providers_apis.dart';
import 'package:afa_app/providers/home_categories_screens_providers_apis/profile_screens_providers_apis/profile_photos_provider_apis.dart';
import 'package:afa_app/providers/home_categories_screens_providers_apis/profile_screens_providers_apis/profile_settings_providers_apis.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ProfileProviders {
  static final ChangeNotifierProvider<ProfilePersonalInformationProvidersApis>
  profilePersonalInformationProvidersApis = ChangeNotifierProvider(
    (ref) => ProfilePersonalInformationProvidersApis(),
  );

  static final ChangeNotifierProvider<ProfileBadgeProvidersApis>
  profileBadgeProvidersApis = ChangeNotifierProvider(
    (ref) => ProfileBadgeProvidersApis(),
  );

  static final ChangeNotifierProvider<CommonApis> commonApis =
      ChangeNotifierProvider((ref) => CommonApis());

  static final ChangeNotifierProvider<ProfileNotesProvidersApis>
  profileNotesProvidersApis = ChangeNotifierProvider(
    (ref) => ProfileNotesProvidersApis(),
  );

  static final ChangeNotifierProvider<ProfilePhotosProviderApis>
  profilePhotosProviderApis = ChangeNotifierProvider(
    (ref) => ProfilePhotosProviderApis(),
  );

  static final ChangeNotifierProvider<ProfileSettingsProvidersApis>
  profileSettingsProvidersApis = ChangeNotifierProvider(
    (ref) => ProfileSettingsProvidersApis(),
  );
}

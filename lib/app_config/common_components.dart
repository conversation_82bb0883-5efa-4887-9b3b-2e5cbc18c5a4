import 'dart:io';

import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

class CommonComponents {
  static String imageNotFound =
      "https://media.istockphoto.com/id/1055079680/vector/black-linear-photo-camera-like-no-image-available.jpg?s=612x612&w=0&k=20&c=P1DebpeMIAtXj_ZbVsKVvg-duuL0v9DlrOZUvPG6UJk=";

  static Widget imageAssetWithCache({
    required BuildContext context,
    required String image,
    required double height,
    required double width,
    required BoxFit fit,
  }) {
    final double pixelRatio = View.of(context).devicePixelRatio;
    return Image.asset(
      image,
      height: height,
      width: width,
      cacheHeight: (height * pixelRatio).round(),
      cacheWidth: (width * pixelRatio).round(),
      fit: fit,
    );
  }

  static Widget imageWithNetworkCache({
    required BuildContext context,
    required String image,
    required double height,
    required double width,
    required BoxFit fit,
  }) {
    return CachedNetworkImage(
      imageUrl: image,
      height: height,
      width: width,
      fit: fit,
      memCacheHeight: (height * View.of(context).devicePixelRatio).round(),
      memCacheWidth: (width * View.of(context).devicePixelRatio).round(),
    );
  }

  static Widget imageFromFile({
    required BuildContext context,
    required File image,
    required double height,
    required double width,
    required BoxFit fit,
  }) {
    return Image.file(
      image,
      height: height,
      width: width,
      cacheHeight: (height * View.of(context).devicePixelRatio).round(),
      cacheWidth: (width * View.of(context).devicePixelRatio).round(),
      fit: fit,
    );
  }

  static Widget comonTitleScreen({
    required BuildContext context,
    required String title,
  }) {
    return Row(
      children: [
        ElevatedButton(
          onPressed: () {
            Navigator.pop(context);
          },

          style: ElevatedButton.styleFrom(
            elevation: 0.0,
            shape: const CircleBorder(),
            foregroundColor: AppColors.blackColor,
            backgroundColor: AppColors.greyColor.withAlpha((0.2 * 255).toInt()),
            minimumSize: Size(40.0.w, 40.0.h),
          ),
          child: Icon(Icons.arrow_back, size: 24.0.h),
        ),
        Text(
          title,
          style: TextStyle(fontSize: 20.0.sp, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  static Future<bool> checkConnectivity() async {
    final List<ConnectivityResult> result = await Connectivity()
        .checkConnectivity();

    if (result[0] == ConnectivityResult.none) {
      return false;
    } else if (result[0] == ConnectivityResult.wifi ||
        result[0] == ConnectivityResult.mobile) {
      return true;
    } else {
      return false;
    }
  }

  static Future<void> loading(BuildContext context) async {
    await showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) => Center(
        child: SizedBox(
          height: 50.0.h,
          width: 50.0.w,
          child: CircularProgressIndicator(
            valueColor: const AlwaysStoppedAnimation(
              AppColors.midLevelGreenColor,
            ),
            strokeWidth: 5.0.w,
          ),
        ),
      ),
    );
  }

  static Widget loadingDataFromServer() => const Center(
    child: CircularProgressIndicator(
      valueColor: AlwaysStoppedAnimation<Color>(AppColors.midLevelGreenColor),
    ),
  );

  static Future showCustomizedAlert({
    required BuildContext context,
    required String title,
    required String subTitle,
  }) async {
    return await showGeneralDialog(
      context: context,
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionDuration: const Duration(milliseconds: 400),
      transitionBuilder: (context, animation1, animation2, child) =>
          ScaleTransition(
            scale: animation1,
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              title: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CommonComponents.imageAssetWithCache(
                    context: context,
                    image: AppImages.logoTestImage,
                    height: 50.0.h,
                    width: 50.0.w,
                    fit: BoxFit.contain,
                  ),
                  SizedBox(height: 5.0.h),
                  Text(
                    title,
                    style: TextStyle(
                      color: AppColors.midLevelGreenColor,
                      fontSize: 16.0.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              content: Text(
                subTitle,
                style: TextStyle(
                  fontSize: 16.0.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.blackColor,
                ),
                textAlign: TextAlign.center,
              ),
              actions: [
                TextButton(
                  style: TextButton.styleFrom(foregroundColor: Colors.green),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text("OK", style: TextStyle(fontSize: 18.0.sp)),
                ),
              ],
            ),
          ),
    );
  }

  static Widget noDataFoundWidget() {
    return Text(
      'No data found',
      style: TextStyle(
        fontSize: 18.0.sp,
        fontWeight: FontWeight.bold,
        color: AppColors.midLevelGreenColor,
      ),
    );
  }

  static Future notConnectionAlert(BuildContext context) async {
    await showCustomizedAlert(
      context: context,
      title: "No Connection To Network",
      subTitle: "Please Connect To Network To Wifi Or Mobile Data",
    );
  }

  static Future timeOutExceptionAlert(BuildContext context) async {
    await showCustomizedAlert(
      context: context,
      title: "Server Busy",
      subTitle: "Network Busy please Try Again Later",
    );
  }

  static Future socketExceptionAlert(BuildContext context) async {
    await CommonComponents.showCustomizedAlert(
      context: context,
      title: "Connection Error",
      subTitle: "Please Make sure your Database Server is connected",
    );
  }

  static Future<bool> saveData({
    required String key,
    required dynamic value,
  }) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    if (value is String) {
      return await prefs.setString(key, value);
    } else if (value is bool) {
      return await prefs.setBool(key, value);
    } else {
      return await prefs.setInt(key, value);
    }
  }

  static Future<dynamic> getSavedData(String key) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.get(key);
  }

  static Future<void> deleteSavedData(String key) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.remove(key);
  }

  static void showCustomizedSnackBar({
    required BuildContext context,
    required String title,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          title,
          style: TextStyle(
            color: Colors.white,
            fontSize: 15.0.sp,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        backgroundColor: AppColors.midLevelGreenColor,
      ),
    );
  }

  static Future<void> launchOnBrowser({
    required BuildContext context,
    required String url,
  }) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      if (!context.mounted) return;
      showCustomizedSnackBar(context: context, title: "Invalid URL");
    }
  }

  static Future<File?> pickImage({
    required BuildContext context,
    required ImageSource source,
  }) async {
    File? image;

    final pickImage = await ImagePicker().pickImage(
      source: source,
      imageQuality: 50,
    );

    if (pickImage != null) {
      image = File(pickImage.path);
      if (context.mounted) {
        showCustomizedSnackBar(
          context: context,
          title: "Image Uploded Successfully",
        );

        return image;
      } else {
        return null;
      }
    } else {
      if (context.mounted) {
        showCustomizedSnackBar(
          context: context,
          title: "Unable To Upload Image",
        );
        return null;
      } else {
        return null;
      }
    }
  }
}

import 'package:afa_app/app_config/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RequestMeetingWidgetsAttendance {
  static Widget requestMeettingFields({
    required BuildContext context,
    required String title,
    required TextEditingController controller,
    required String validate,
    bool showDateIcon = false,
    bool showTimeIcon = false,
    int maxlines = 1,
    void Function()? onPress,
    bool readOnly = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 14.0.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.blackColor,
          ),
        ),
        SizedBox(height: 5.0.h),
        TextFormField(
          controller: controller,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          maxLines: maxlines,
          readOnly: readOnly,
          onTap: onPress,
          validator: (value) => value!.isEmpty ? validate : null,
          style: TextStyle(fontSize: 12.0.sp),
          decoration: InputDecoration(
            hintText: "Enter",
            hintStyle: TextStyle(fontSize: 12.0.sp, color: AppColors.greyColor),
            border: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColors.greyColor),
              borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColors.greyColor),
              borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColors.greyColor),
              borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            ),
            suffixIcon: showDateIcon
                ? Icon(Icons.date_range, size: 17.0.h)
                : showTimeIcon
                ? Icon(Icons.alarm_sharp, size: 17.0.h)
                : const SizedBox(),
            suffixIconColor: AppColors.midLevelGreenColor,
          ),
        ),
      ],
    );
  }
}

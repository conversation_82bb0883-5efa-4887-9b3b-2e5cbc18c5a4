import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_providers/profile_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class ProfileScreenWidgets {
  static Widget profileFileds({
    required BuildContext context,
    required String image,
    required String title,
    required Function() onPress,
    Color filedColor = AppColors.lightGreenColor,
    Color textColor = AppColors.blackColor,
    Color arrowColor = AppColors.midLevelGreenColor,
  }) {
    return InkWell(
      onTap: onPress,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                alignment: Alignment.center,
                padding: EdgeInsets.all(10.0.h),
                decoration: BoxDecoration(
                  color: filedColor,
                  shape: BoxShape.circle,
                ),
                child: CommonComponents.imageAssetWithCache(
                  context: context,
                  image: image,
                  height: 20.0.h,
                  width: 20.0.w,
                  fit: BoxFit.contain,
                ),
              ),
              SizedBox(width: 10.0.w),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14.0.sp,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
              ),
            ],
          ),
          Icon(Icons.arrow_forward_ios, size: 25.0.h, color: arrowColor),
        ],
      ),
    );
  }

  static Future logOutAlert({required BuildContext context}) async {
    return await showGeneralDialog(
      context: context,
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionDuration: const Duration(milliseconds: 400),
      transitionBuilder: (context, animation1, animation2, child) =>
          ScaleTransition(
            scale: animation1,
            child: AlertDialog(
              backgroundColor: Colors.white,
              // contentPadding: EdgeInsets.all(15.0.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              title: const Center(child: Text("Log out")),
              content: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: CommonComponents.imageAssetWithCache(
                        context: context,
                        image: AppImages.logoutAlertIcon,
                        height: 140.0.h,
                        width: 140.0.w,
                        fit: BoxFit.contain,
                      ),
                    ),
                    SizedBox(height: 10.0.h),
                    Text(
                      "Are you sure you want to logout?",
                      style: TextStyle(
                        fontSize: 20.0.sp,
                        color: AppColors.blackColor,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 20.0.h),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },

                            style: ElevatedButton.styleFrom(
                              foregroundColor: Colors.white,
                              backgroundColor: AppColors.midLevelGreenColor,
                              textStyle: TextStyle(
                                fontSize: 16.0.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            child: const Text("Undo"),
                          ),
                        ),
                        SizedBox(width: 10.0.w),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () async {
                              await CommonComponents.deleteSavedData(
                                ApiKeys.userName,
                              );
                              await CommonComponents.deleteSavedData(
                                ApiKeys.userID,
                              );
                              await CommonComponents.deleteSavedData(
                                ApiKeys.userToken,
                              );
                              if (!context.mounted) return;
                              Navigator.pushNamedAndRemoveUntil(
                                context,
                                PATHS.splashScreen,
                                (value) => false,
                              );
                            },

                            style: ElevatedButton.styleFrom(
                              foregroundColor: Colors.white,
                              backgroundColor: Colors.red,
                              textStyle: TextStyle(
                                fontSize: 16.0.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            child: const Text("Logout"),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
    );
  }

  static Future showNoteAlertWidget({
    required BuildContext context,
    required TextEditingController controller,
    required GlobalKey<FormState> formKey,
  }) async {
    return await showGeneralDialog(
      context: context,
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionDuration: const Duration(milliseconds: 400),
      transitionBuilder: (context, animation1, animation2, child) =>
          ScaleTransition(
            scale: animation1,
            child: AlertDialog(
              contentPadding: EdgeInsets.all(10.0.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              backgroundColor: Colors.white,
              title: Center(
                child: Text(
                  "Add A Note",
                  style: TextStyle(
                    fontSize: 20.0.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.blackColor,
                  ),
                ),
              ),
              content: Form(
                key: formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "Note",
                      style: TextStyle(
                        fontSize: 14.0.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 5.0.h),
                    TextFormField(
                      controller: controller,
                      validator: (value) =>
                          value!.isEmpty ? "Please Enter Note" : null,
                      maxLines: 3,
                      style: TextStyle(
                        fontSize: 12.0.sp,
                        fontWeight: FontWeight.bold,
                      ),
                      decoration: InputDecoration(
                        hintText: "Enter",
                        hintStyle: TextStyle(
                          fontSize: 12.0.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.greyColor,
                        ),
                        border: OutlineInputBorder(
                          borderSide: const BorderSide(
                            color: AppColors.greyColor,
                          ),
                          borderRadius: BorderRadius.all(
                            Radius.circular(16.0.r),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 25.0.h),
                    ElevatedButton(
                      onPressed: () async {
                        if (formKey.currentState!.validate()) {
                          await context
                              .read(ProfileProviders.commonApis)
                              .sendNote(context: context, note: controller.text)
                              .then((value) => controller.clear());
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: AppColors.midLevelGreenColor,
                        textStyle: TextStyle(
                          fontSize: 16.0.sp,
                          fontWeight: FontWeight.bold,
                        ),
                        minimumSize: Size(398.0.w, 46.0.h),
                      ),
                      child: const Text("Save"),
                    ),
                  ],
                ),
              ),
            ),
          ),
    );
  }
}

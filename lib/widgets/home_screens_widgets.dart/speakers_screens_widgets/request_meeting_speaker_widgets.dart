import 'package:afa_app/app_config/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class RequestMeetingSpeakerWidgets {
  static Widget requestMeettingFields({
    required BuildContext context,
    required String title,
    required TextEditingController controller,
    required String validate,
    bool showDateIcon = false,
    bool showTimeIcon = false,
    bool readOnly = false,
    int maxlines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 14.0.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.blackColor,
          ),
        ),
        SizedBox(height: 5.0.h),
        TextFormField(
          controller: controller,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          maxLines: maxlines,
          readOnly: readOnly,
          validator: (value) => value!.isEmpty ? validate : null,
          style: TextStyle(fontSize: 12.0.sp),
          decoration: InputDecoration(
            hintText: "Enter",
            hintStyle: TextStyle(fontSize: 12.0.sp, color: AppColors.greyColor),
            border: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColors.greyColor),
              borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColors.greyColor),
              borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColors.greyColor),
              borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            ),
            suffixIcon: showDateIcon
                ? IconButton(
                    onPressed: () async {
                      await showDatePicker(
                        context: context,
                        firstDate: DateTime.now(),
                        lastDate: DateTime(2050),
                      ).then((value) {
                        if (value != null) {
                          controller.text = DateFormat(
                            'd/M/yyyy',
                          ).format(value);
                        }
                      });
                    },
                    icon: const Icon(Icons.date_range),
                    iconSize: 17.0.h,
                  )
                : showTimeIcon
                ? IconButton(
                    onPressed: () async {
                      await showTimePicker(
                        context: context,
                        initialTime: TimeOfDay.now(),
                      ).then((value) {
                        if (value != null) {
                          if (!context.mounted) return;
                          controller.text = value.format(context);
                        }
                      });
                    },
                    icon: const Icon(Icons.alarm_sharp),
                    iconSize: 17.0.h,
                  )
                : const SizedBox(),
            suffixIconColor: AppColors.midLevelGreenColor,
          ),
        ),
      ],
    );
  }
}

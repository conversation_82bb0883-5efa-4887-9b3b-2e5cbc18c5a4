import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/home_screens_models/sessions_q&a_models/question_and_answers_speakers_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class SessionQuestionsScreenWidgets {
  static Widget bubleDaysWidget({
    required BuildContext context,
    required String title,
    required String subTitle,
    required Color textColor,
    required Color containerColor,
  }) {
    return Container(
      padding: EdgeInsets.all(15.0.h),
      alignment: Alignment.center,

      decoration: BoxDecoration(shape: BoxShape.circle, color: containerColor),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          Text(
            subTitle,
            style: TextStyle(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }

  static Widget showSpeakersListWidget({
    required BuildContext context,
    required String image,
    required List<QandASpeakersModel> speakerList,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Speakers",
          style: TextStyle(
            fontSize: 12.0.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.greyColor,
          ),
        ),
        SizedBox(height: 5.0.h),
        SizedBox(
          height: 30.0.h,
          width: speakerList.length * 32.0.w,
          child: ListView.separated(
            separatorBuilder: (context, index) => SizedBox(width: 2.0.w),
            padding: EdgeInsets.zero,
            scrollDirection: Axis.horizontal,
            itemCount: speakerList.length,

            itemBuilder: (context, index) => InkWell(
              onTap: () {
                // Navigator.pushNamed(
                //   context,
                //   PATHS.agendaDetailsSpeakerScreen,
                //   arguments: AgendaDetailsSpeakerScreen(
                //     speakerId: speakerList[index].speakerID,
                //   ),
                // );
              },
              child: ClipOval(
                child: speakerList[index].speakerImage != null
                    ? CommonComponents.imageWithNetworkCache(
                        context: context,
                        image: speakerList[index].speakerImage!,
                        height: 24.0.h,
                        width: 24.0.w,
                        fit: BoxFit.contain,
                      )
                    : CommonComponents.imageAssetWithCache(
                        context: context,
                        image: AppImages.userPhoto,
                        height: 30.0.h,
                        width: 30.0.w,
                        fit: BoxFit.contain,
                      ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  static Future showAddQuestionAlertWidget({
    required BuildContext context,
    required TextEditingController controller,
    required GlobalKey<FormState> formKey,
    required int sessionID,
  }) async {
    return await showGeneralDialog(
      context: context,
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionDuration: const Duration(milliseconds: 400),
      transitionBuilder: (context, animation1, animation2, child) =>
          ScaleTransition(
            scale: animation1,
            child: AlertDialog(
              contentPadding: EdgeInsets.all(10.0.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              backgroundColor: Colors.white,
              title: Center(
                child: Text(
                  "Add Question",
                  style: TextStyle(
                    fontSize: 20.0.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.blackColor,
                  ),
                ),
              ),
              content: Form(
                key: formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "Question",
                      style: TextStyle(
                        fontSize: 14.0.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 5.0.h),
                    TextFormField(
                      controller: controller,
                      validator: (value) =>
                          value!.isEmpty ? "Please Enter Your Question" : null,
                      maxLines: 3,
                      style: TextStyle(
                        fontSize: 12.0.sp,
                        fontWeight: FontWeight.bold,
                      ),
                      decoration: InputDecoration(
                        hintText: "Enter",
                        hintStyle: TextStyle(
                          fontSize: 12.0.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.greyColor,
                        ),
                        border: OutlineInputBorder(
                          borderSide: const BorderSide(
                            color: AppColors.greyColor,
                          ),
                          borderRadius: BorderRadius.all(
                            Radius.circular(16.0.r),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 25.0.h),
                    ElevatedButton(
                      onPressed: () async {
                        if (formKey.currentState!.validate()) {
                          await context
                              .read(
                                HomeScreenCategoriesProviders
                                    .questionAndAnswersProvidersApis,
                              )
                              .createQuestion(
                                context: context,
                                question: controller.text,
                                sessionID: sessionID,
                              );
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: AppColors.midLevelGreenColor,
                        textStyle: TextStyle(
                          fontSize: 16.0.sp,
                          fontWeight: FontWeight.bold,
                        ),
                        minimumSize: Size(398.0.w, 46.0.h),
                      ),
                      child: const Text("Save"),
                    ),
                  ],
                ),
              ),
            ),
          ),
    );
  }
}

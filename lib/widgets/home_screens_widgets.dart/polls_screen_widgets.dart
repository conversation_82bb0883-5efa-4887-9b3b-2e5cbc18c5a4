import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/polls_screen_models/polls_questions_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_polls/flutter_polls.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class PollsScreenWidgets {
  static Widget tabBarWidget({required String title, required Color color}) {
    return Container(
      padding: EdgeInsets.all(5.0.h),
      width: 130.0.w,

      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(20.0.r)),
        color: color,
      ),
      child: Text(title),
    );
  }

  static Widget pollsWidget({
    required BuildContext context,

    required GlobalKey<FormState> formKey,
    required TextEditingController questionController,
    required List<PollsQuestionsModel> pollsQuestions,
    required List<TextEditingController> optionsController,
    required void Function() onPress,
  }) {
    return Column(
      children: [
        ElevatedButton.icon(
          onPressed: () async {
            await showGeneralDialog(
              context: context,

              pageBuilder: (context, animation1, animation2) => Container(),
              transitionDuration: const Duration(milliseconds: 400),
              transitionBuilder: (context, animation1, animation2, child) =>
                  ScaleTransition(
                    scale: animation1,
                    child: AlertDialog(
                      backgroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20.0),
                      ),

                      insetPadding: EdgeInsets.all(10.0.h),
                      title: Text(
                        "Share a poll",
                        style: TextStyle(
                          fontSize: 20.0.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.blackColor,
                        ),
                      ),
                      content: Form(
                        key: formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              "Question",
                              style: TextStyle(
                                fontSize: 14.0.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(height: 5.0.h),
                            TextFormField(
                              controller: questionController,
                              validator: (value) => value!.isEmpty
                                  ? "Please Enter Question"
                                  : null,

                              style: TextStyle(
                                fontSize: 12.0.sp,
                                fontWeight: FontWeight.bold,
                              ),
                              decoration: InputDecoration(
                                hintText: "Enter",
                                hintStyle: TextStyle(
                                  fontSize: 12.0.sp,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.greyColor,
                                ),
                                border: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: AppColors.greyColor,
                                  ),
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(16.0.r),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(height: 10.0.h),
                            Text(
                              "Add Answer",
                              style: TextStyle(
                                fontSize: 14.0.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),

                            SizedBox(height: 5.0.h),
                            SizedBox(
                              height: 230.0.h,
                              width: 398.0.w,

                              child: ListView.separated(
                                padding: EdgeInsets.zero,
                                physics: const ScrollPhysics(),
                                separatorBuilder: (context, index) =>
                                    SizedBox(height: 10.0.h),
                                itemCount: 3,
                                itemBuilder: (context, index) => TextFormField(
                                  controller: optionsController[index],
                                  validator: (value) => value!.isEmpty
                                      ? "Please Enter option ${index + 1}"
                                      : null,

                                  style: TextStyle(
                                    fontSize: 12.0.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  decoration: InputDecoration(
                                    hintText: "Enter",
                                    hintStyle: TextStyle(
                                      fontSize: 12.0.sp,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.greyColor,
                                    ),
                                    border: OutlineInputBorder(
                                      borderSide: const BorderSide(
                                        color: AppColors.greyColor,
                                      ),
                                      borderRadius: BorderRadius.all(
                                        Radius.circular(16.0.r),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            ElevatedButton(
                              onPressed: onPress,
                              style: ElevatedButton.styleFrom(
                                foregroundColor: Colors.white,
                                backgroundColor: AppColors.midLevelGreenColor,
                                textStyle: TextStyle(
                                  fontSize: 16.0.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                                minimumSize: Size(398.0.w, 46.0.h),
                              ),
                              child: const Text("Post"),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
            );
          },
          label: const Text("Share a poll"),
          icon: CommonComponents.imageAssetWithCache(
            context: context,
            image: AppImages.sharePollIcon,
            height: 23.0.h,
            width: 23.0.w,
            fit: BoxFit.contain,
          ),
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: AppColors.midLevelGreenColor,
            minimumSize: Size(398.0.w, 48.0.h),
            textStyle: TextStyle(
              fontSize: 16.0.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        SizedBox(height: 20.0.h),

        ListView.separated(
          // padding: EdgeInsets.all(10.0.h),
          physics: const BouncingScrollPhysics(),
          shrinkWrap: true,
          separatorBuilder: (context, index) => SizedBox(height: 15.0.h),
          itemCount: pollsQuestions.length,

          itemBuilder: (context, index) => Container(
            padding: EdgeInsets.all(10.0.h),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.greyColor),
              borderRadius: BorderRadius.all(Radius.circular(24.0.r)),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        ClipOval(
                          child: CommonComponents.imageWithNetworkCache(
                            context: context,
                            image: pollsQuestions[index].userImage!,
                            height: 40.0.h,
                            width: 40.0.h,
                            fit: BoxFit.contain,
                          ),
                        ),
                        SizedBox(width: 5.0.w),
                        Text(
                          pollsQuestions[index].userName!,
                          style: TextStyle(
                            fontSize: 14.0.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    // IconButton(
                    //   onPressed: () {},
                    //   icon: const Icon(Icons.more_vert),
                    //   iconSize: 20.0.h,
                    //   color: AppColors.midLevelGreenColor,
                    //   padding: EdgeInsets.zero,
                    //   constraints: BoxConstraints(
                    //     maxWidth: 10.0.w,
                    //     minWidth: 10.0.w,
                    //   ),
                    // ),
                  ],
                ),
                FlutterPolls(
                  pollId: pollsQuestions[index].pollID!.toString(), //postId
                  hasVoted: false,

                  votedBackgroundColor: Colors.white,

                  leadingVotedProgessColor: AppColors.midLevelGreenColor,
                  votedPollOptionsBorder: BoxBorder.all(color: Colors.white),
                  pollOptionsBorder: BoxBorder.all(color: Colors.white),
                  votedCheckmark: const SizedBox(),

                  pollTitle: Text(
                    pollsQuestions[index].question!,
                    style: TextStyle(
                      fontSize: 14.0.sp,
                      color: AppColors.blackColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  pollOptions: pollsQuestions[index].pollsOption!
                      .map(
                        (options) => PollOption(
                          id: options.optionID.toString(),
                          title: Text(
                            options.optionTitle!,
                            style: TextStyle(
                              fontSize: 12.0.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.midLevelGreenColor,
                            ),
                          ),
                          votes: options.votesCount!,
                        ),
                      )
                      .toList(),
                  onVoted: (PollOption option, int vote) async {
                    await context
                        .read(
                          HomeScreenCategoriesProviders
                              .pollsScreenProvidersApis,
                        )
                        .userMakeVote(
                          context: context,
                          pollID: pollsQuestions[index].pollID!,
                          optionID: option.id!,
                        );
                    return true;
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

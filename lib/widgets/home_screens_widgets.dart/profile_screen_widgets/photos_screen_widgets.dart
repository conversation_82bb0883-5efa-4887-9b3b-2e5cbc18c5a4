import 'dart:io';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:riverpod_context/riverpod_context.dart';

class PhotosScreenWidgets {
  static Future sharePhotoAlert({
    required BuildContext context,
    required void Function() onPress,
    required StateProvider<File?> photo,
  }) async {
    return await showGeneralDialog(
      context: context,
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionDuration: const Duration(milliseconds: 400),
      transitionBuilder: (context, animation1, animation2, child) =>
          ScaleTransition(
            scale: animation1,
            child: AlertDialog(
              backgroundColor: Colors.white,
              contentPadding: EdgeInsets.all(15.0.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              title: const Center(child: Text("Share a photo")),
              content: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    InkWell(
                      onTap: () async {
                        context
                            .read(photo.notifier)
                            .state = await CommonComponents.pickImage(
                          context: context,
                          source: ImageSource.gallery,
                        );
                      },
                      child: Consumer(
                        builder: (context, watch, child) => Container(
                          height: 108,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.greyColor),
                            borderRadius: BorderRadius.all(
                              Radius.circular(32.0.r),
                            ),
                          ),
                          child: Center(
                            child: watch.watch(photo) != null
                                ? ClipRRect(
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(16.0.r),
                                    ),
                                    child: CommonComponents.imageFromFile(
                                      context: context,
                                      image: watch.watch(photo.notifier).state!,
                                      height: 100.0.h,
                                      width: 300.0.w,
                                      fit: BoxFit.contain,
                                    ),
                                  )
                                : CommonComponents.imageAssetWithCache(
                                    context: context,
                                    image: AppImages.uploadPhotoIcon,
                                    height: 40.0.h,
                                    width: 40.0.w,
                                    fit: BoxFit.contain,
                                  ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 10.0.h),
                    ElevatedButton(
                      onPressed: onPress,

                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: AppColors.midLevelGreenColor,
                        textStyle: TextStyle(
                          fontSize: 16.0.sp,
                          fontWeight: FontWeight.bold,
                        ),
                        minimumSize: Size(398.0.w, 46.0.h),
                      ),
                      child: const Text("Post"),
                    ),
                  ],
                ),
              ),
            ),
          ),
    );
  }
}

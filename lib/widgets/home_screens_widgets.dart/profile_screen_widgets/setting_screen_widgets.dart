import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class SettingScreenWidgets {
  static Widget settingsFileldsDropDownWidget({
    required Future<List<dynamic>> Function(dynamic) dataList,
    required String Function(dynamic) items,
    required void Function(dynamic) onChanged,
    dynamic selectedItem,
  }) {
    return DropdownSearch(
      validator: (value) => value == null ? "Please Choose Country" : null,
      selectedItem: selectedItem,
      items: (filter, scrollProps) async => await dataList(filter),
      itemAsString: items,
      popupProps: const PopupProps.menu(fit: FlexFit.loose),
      autoValidateMode: AutovalidateMode.onUserInteraction,
      compareFn: (a, b) => a == b,
      decoratorProps: DropDownDecoratorProps(
        decoration: InputDecoration(
          hintText: "Enter",
          hintStyle: TextStyle(fontSize: 12.0.sp),
          errorStyle: TextStyle(fontSize: 14.0.sp),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            borderSide: const BorderSide(color: AppColors.greyColor),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            borderSide: const BorderSide(color: AppColors.greyColor),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            borderSide: const BorderSide(color: AppColors.greyColor),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            borderSide: const BorderSide(color: AppColors.greyColor),
          ),
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            borderSide: const BorderSide(color: AppColors.greyColor),
          ),
        ),
      ),

      onChanged: onChanged,
    );
  }

  static Widget settingFiledsWidget({
    required String title,
    required TextEditingController controller,
    String? validate,
    required TextInputType type,
    int maxLines = 1,
    required TextInputAction action,
    required String hint,
    bool showPasswordIcon = false,
  }) {
    final StateProvider<bool> showPassword = StateProvider((ref) => true);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 14.0.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.blackColor,
          ),
        ),
        SizedBox(height: 10.0.h),

        Consumer(
          builder: (context, watch, child) => TextFormField(
            controller: controller,
            autovalidateMode: AutovalidateMode.onUserInteraction,

            validator: (value) => value!.isEmpty ? validate : null,
            keyboardType: type,
            obscureText: showPasswordIcon ? watch.watch(showPassword) : false,
            style: TextStyle(fontSize: 12.0.sp),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(fontSize: 12.0.sp),
              errorStyle: TextStyle(fontSize: 14.0.sp),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
                borderSide: const BorderSide(color: AppColors.greyColor),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
                borderSide: const BorderSide(color: AppColors.greyColor),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
                borderSide: const BorderSide(color: AppColors.greyColor),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
                borderSide: const BorderSide(color: AppColors.greyColor),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
                borderSide: const BorderSide(color: AppColors.greyColor),
              ),
              suffixIcon: Visibility(
                visible: showPasswordIcon,
                child: InkWell(
                  onTap: () {
                    context.read(showPassword.notifier).state = !context
                        .read(showPassword.notifier)
                        .state;
                  },
                  child: watch.watch(showPassword)
                      ? const Icon(Icons.visibility_off)
                      : const Icon(Icons.visibility),
                ),
              ),
              suffixIconColor: AppColors.midLevelGreenColor,
            ),
          ),
        ),
      ],
    );
  }

  static Future deleteAccountAlert({
    required BuildContext context,
    required TextEditingController passwordController,
    required GlobalKey<FormState> formKey,
  }) async {
    return await showGeneralDialog(
      context: context,
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionDuration: const Duration(milliseconds: 400),
      transitionBuilder: (context, animation1, animation2, child) =>
          ScaleTransition(
            scale: animation1,
            child: AlertDialog(
              backgroundColor: Colors.white,
              contentPadding: EdgeInsets.all(15.0.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              title: const Center(child: Text("Delete Account")),
              content: SingleChildScrollView(
                child: Form(
                  key: formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Center(
                        child: CommonComponents.imageAssetWithCache(
                          context: context,
                          image: AppImages.deleteAccAlertIcon,
                          height: 140.0.h,
                          width: 140.0.w,
                          fit: BoxFit.contain,
                        ),
                      ),
                      SizedBox(height: 10.0.h),
                      Text(
                        "Are you sure you want to delete your account?",
                        style: TextStyle(
                          fontSize: 20.0.sp,
                          color: AppColors.blackColor,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 20.0.h),
                      settingFiledsWidget(
                        title: "Password",
                        controller: passwordController,
                        validate: "Please Enter Password",
                        type: TextInputType.visiblePassword,
                        action: TextInputAction.done,
                        hint: "Enter",
                        showPasswordIcon: true,
                      ),
                      SizedBox(height: 20.0.h),
                      ElevatedButton(
                        onPressed: () {
                          if (formKey.currentState!.validate()) {
                            Navigator.pop(context);
                          }
                        },

                        style: ElevatedButton.styleFrom(
                          foregroundColor: Colors.white,
                          backgroundColor: AppColors.midLevelGreenColor,
                          textStyle: TextStyle(
                            fontSize: 16.0.sp,
                            fontWeight: FontWeight.bold,
                          ),
                          minimumSize: Size(398.0.w, 46.0.h),
                        ),
                        child: const Text("Save"),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
    );
  }
}

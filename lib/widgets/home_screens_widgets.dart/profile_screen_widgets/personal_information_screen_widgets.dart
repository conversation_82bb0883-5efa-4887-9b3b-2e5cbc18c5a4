import 'package:afa_app/app_config/api_providers/profile_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:riverpod_context/riverpod_context.dart';

class PersonalInformationScreenWidgets {
  static Widget profileFields({
    required BuildContext context,
    required String image,
    required String title,

    required Function() onPressAdd,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                CommonComponents.imageAssetWithCache(
                  context: context,
                  image: image,
                  height: 24.0.h,
                  width: 24.0.w,
                  fit: BoxFit.contain,
                ),
                SizedBox(width: 10.0.w),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18.0.sp,
                    color: AppColors.blackColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            InkWell(
              onTap: onPressAdd,
              child: Container(
                padding: EdgeInsets.all(10.0.h),
                decoration: const BoxDecoration(
                  color: AppColors.lightGreenColor,
                  shape: BoxShape.circle,
                ),
                child: CommonComponents.imageAssetWithCache(
                  context: context,
                  image: AppImages.addFileIcon,
                  height: 20.0.h,
                  width: 20.0.w,
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  static Widget profileDropdownWidget({
    required String hint,
    required String validate,
    required Future<List<dynamic>> Function(dynamic) dataList,
    required String Function(dynamic) items,
    required void Function(dynamic) onChanged,
    dynamic selectedItem,
    Key? key,
  }) {
    return DropdownSearch<dynamic>(
      key: key,
      validator: (value) => value == null ? validate : null,
      selectedItem: selectedItem,
      items: (filter, scrollProps) async => await dataList(filter),
      itemAsString: items,
      popupProps: const PopupProps.menu(fit: FlexFit.loose),
      autoValidateMode: AutovalidateMode.onUserInteraction,
      compareFn: (a, b) => a == b,
      decoratorProps: DropDownDecoratorProps(
        baseStyle: TextStyle(fontSize: 14.0.sp),
        decoration: InputDecoration(
          hintText: hint,

          labelStyle: TextStyle(fontSize: 14.0.sp, color: AppColors.greyColor),

          suffixIcon: Icon(
            Icons.keyboard_arrow_down_outlined,
            size: 25.0.h,
            color: AppColors.midLevelGreenColor,
          ),

          hintStyle: TextStyle(fontSize: 12.0.sp, color: AppColors.greyColor),
          border: OutlineInputBorder(
            borderSide: const BorderSide(color: AppColors.greyColor),
            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: const BorderSide(color: AppColors.greyColor),
            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: const BorderSide(color: AppColors.greyColor),
            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
          ),
        ),
      ),
      onChanged: onChanged,
    );
  }

  static Widget addFormsWidget({
    required BuildContext context,
    required String title,
    required TextEditingController controller,
    required String validate,
    bool readOnly = false,
    bool showDateIcon = false,
    bool showTimeIcon = false,
    int maxlines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 14.0.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.blackColor,
          ),
        ),
        SizedBox(height: 5.0.h),
        TextFormField(
          controller: controller,
          readOnly: readOnly,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          maxLines: maxlines,
          validator: (value) => value!.isEmpty ? validate : null,
          style: TextStyle(fontSize: 12.0.sp),
          decoration: InputDecoration(
            hintText: "Enter",
            hintStyle: TextStyle(fontSize: 12.0.sp, color: AppColors.greyColor),
            border: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColors.greyColor),
              borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColors.greyColor),
              borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: AppColors.greyColor),
              borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            ),
            suffixIcon: showDateIcon
                ? IconButton(
                    onPressed: () async {
                      await showDatePicker(
                        context: context,
                        firstDate: DateTime.now(),
                        lastDate: DateTime(2050),
                      ).then((value) {
                        if (value != null) {
                          controller.text = DateFormat(
                            'd/M/yyyy',
                          ).format(value);
                        }
                      });
                    },
                    icon: const Icon(Icons.date_range),
                    iconSize: 17.0.h,
                  )
                : showTimeIcon
                ? IconButton(
                    onPressed: () async {
                      await showTimePicker(
                        context: context,
                        initialTime: TimeOfDay.now(),
                      ).then((value) {
                        if (value != null) {
                          controller.text = value.toString();
                        }
                      });
                    },
                    icon: const Icon(Icons.alarm_sharp),
                    iconSize: 17.0.h,
                  )
                : const SizedBox(),
            suffixIconColor: AppColors.midLevelGreenColor,
          ),

          onTap: () {},
        ),
      ],
    );
  }

  static Future addAffiliationAlert({
    required BuildContext context,
    required TextEditingController titleController,
    required TextEditingController startDateController,
    required TextEditingController endDateController,
    required GlobalKey<FormState> affiliationFormKey,
    required void Function() onPress,
  }) async {
    return await showGeneralDialog(
      context: context,
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionDuration: const Duration(milliseconds: 400),
      transitionBuilder: (context, animation1, animation2, child) =>
          ScaleTransition(
            scale: animation1,
            child: AlertDialog(
              backgroundColor: Colors.white,
              contentPadding: EdgeInsets.all(15.0.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              title: const Center(child: Text("Add Affiliation")),
              content: SingleChildScrollView(
                child: Form(
                  key: affiliationFormKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      profileDropdownWidget(
                        hint: "Choose Company",
                        validate: "Please Choose Company",
                        selectedItem: context
                            .read(
                              ProfileProviders
                                  .profilePersonalInformationProvidersApis,
                            )
                            .companySelected,
                        dataList: (companies) async => await context
                            .read(
                              ProfileProviders
                                  .profilePersonalInformationProvidersApis,
                            )
                            .getAllCompanies(context: context),
                        items: (company) => company.companyName,
                        onChanged: (value) {
                          context
                              .read(
                                ProfileProviders
                                    .profilePersonalInformationProvidersApis,
                              )
                              .setCompanySelected(value);
                        },
                      ),

                      SizedBox(height: 10.0.h),
                      addFormsWidget(
                        context: context,
                        title: "Title",
                        controller: titleController,
                        validate: "Please Enter Title",
                      ),
                      SizedBox(height: 10.0.h),
                      addFormsWidget(
                        context: context,
                        title: "Start Date",
                        controller: startDateController,
                        validate: "Please Enter Start Date",
                        showDateIcon: true,
                        readOnly: true,
                      ),
                      SizedBox(height: 10.0.h),
                      addFormsWidget(
                        context: context,
                        title: "End Date",
                        controller: endDateController,
                        validate: "Please Enter End Date",
                        showDateIcon: true,
                        readOnly: true,
                      ),
                      SizedBox(height: 20.0.h),
                      ElevatedButton(
                        onPressed: onPress,

                        style: ElevatedButton.styleFrom(
                          foregroundColor: Colors.white,
                          backgroundColor: AppColors.midLevelGreenColor,
                          textStyle: TextStyle(
                            fontSize: 16.0.sp,
                            fontWeight: FontWeight.bold,
                          ),
                          minimumSize: Size(398.0.w, 46.0.h),
                        ),
                        child: const Text("Save"),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
    );
  }

  static Future addAndEditEduactionAlert({
    required BuildContext context,
    required TextEditingController schoolNameController,
    required TextEditingController degreeController,
    required TextEditingController majorController,
    required TextEditingController startDateController,
    required TextEditingController endDateController,
    required GlobalKey<FormState> educationFormKey,
    required void Function()? onPress,
  }) async {
    return await showGeneralDialog(
      context: context,
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionDuration: const Duration(milliseconds: 400),
      transitionBuilder: (context, animation1, animation2, child) =>
          ScaleTransition(
            scale: animation1,
            child: AlertDialog(
              backgroundColor: Colors.white,
              contentPadding: EdgeInsets.all(15.0.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              title: const Center(child: Text("Add Education")),
              content: SingleChildScrollView(
                child: Form(
                  key: educationFormKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      addFormsWidget(
                        context: context,
                        title: "School Name",
                        controller: schoolNameController,
                        validate: "Please Enter School Name",
                      ),
                      SizedBox(height: 10.0.h),
                      addFormsWidget(
                        context: context,
                        title: "Degree",
                        controller: degreeController,
                        validate: "Please Enter Degree",
                      ),
                      SizedBox(height: 10.0.h),
                      addFormsWidget(
                        context: context,
                        title: "Major",
                        controller: majorController,
                        validate: "Please Enter Major",
                      ),
                      SizedBox(height: 10.0.h),
                      addFormsWidget(
                        context: context,
                        title: "Start Date",
                        controller: startDateController,
                        validate: "Please Enter Start Date",
                        showDateIcon: true,
                        readOnly: true,
                      ),
                      SizedBox(height: 10.0.h),
                      addFormsWidget(
                        context: context,
                        title: "End Date",
                        controller: endDateController,
                        validate: "Please Enter End Date",
                        showDateIcon: true,
                        readOnly: true,
                      ),
                      SizedBox(height: 20.0.h),
                      ElevatedButton(
                        onPressed: onPress,

                        style: ElevatedButton.styleFrom(
                          foregroundColor: Colors.white,
                          backgroundColor: AppColors.midLevelGreenColor,
                          textStyle: TextStyle(
                            fontSize: 16.0.sp,
                            fontWeight: FontWeight.bold,
                          ),
                          minimumSize: Size(398.0.w, 46.0.h),
                        ),
                        child: const Text("Save"),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
    );
  }

  // static Future addInterestAlert({
  //   required BuildContext context,
  //   required TextEditingController interestController,
  // }) async {
  //   return await showGeneralDialog(
  //     context: context,
  //     pageBuilder: (context, animation1, animation2) => Container(),
  //     transitionDuration: const Duration(milliseconds: 400),
  //     transitionBuilder: (context, animation1, animation2, child) =>
  //         ScaleTransition(
  //           scale: animation1,
  //           child: AlertDialog(
  //             backgroundColor: Colors.white,
  //             contentPadding: EdgeInsets.all(15.0.h),
  //             shape: RoundedRectangleBorder(
  //               borderRadius: BorderRadius.circular(20.0),
  //             ),
  //             title: const Center(child: Text("Add Interests")),
  //             content: SingleChildScrollView(
  //               child: Column(
  //                 crossAxisAlignment: CrossAxisAlignment.start,
  //                 children: [
  //                   addFormsWidget(
  //                     context: context,
  //                     title: "Search Interests",
  //                     controller: interestController,
  //                     validate: "Please Enter Search Interests",
  //                   ),
  //                   SizedBox(height: 20.0.h),
  //                   Wrap(
  //                     children: [
  //                       Container(
  //                         alignment: Alignment.center,
  //                         padding: EdgeInsets.all(10.0.h),
  //                         height: 40.0.h,
  //                         width: 100.0.w,

  //                         decoration: BoxDecoration(
  //                           borderRadius: BorderRadius.all(
  //                             Radius.circular(24.0.r),
  //                           ),
  //                           color: AppColors.lightgreyColor,
  //                         ),
  //                         child: Row(
  //                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                           children: [
  //                             Text(
  //                               "Travel",
  //                               style: TextStyle(
  //                                 fontSize: 14.0.sp,
  //                                 fontWeight: FontWeight.bold,
  //                               ),
  //                             ),
  //                             SizedBox(width: 10.0.w),
  //                             InkWell(
  //                               onTap: () {},
  //                               child: Icon(
  //                                 Icons.close,
  //                                 color: AppColors.blackColor,
  //                                 size: 14.0.h,
  //                               ),
  //                             ),
  //                           ],
  //                         ),
  //                       ),
  //                     ],
  //                   ),

  //                   SizedBox(height: 20.0.h),
  //                   ElevatedButton(
  //                     onPressed: () {
  //                       Navigator.pop(context);
  //                     },

  //                     style: ElevatedButton.styleFrom(
  //                       foregroundColor: Colors.white,
  //                       backgroundColor: AppColors.midLevelGreenColor,
  //                       textStyle: TextStyle(
  //                         fontSize: 16.0.sp,
  //                         fontWeight: FontWeight.bold,
  //                       ),
  //                       minimumSize: Size(398.0.w, 46.0.h),
  //                     ),
  //                     child: const Text("Save"),
  //                   ),
  //                 ],
  //               ),
  //             ),
  //           ),
  //         ),
  //   );
  // }

  static Future addAndEditSocialLinkAlert({
    required BuildContext context,
    required TextEditingController searchSocialController,
    required TextEditingController urlController,
    required GlobalKey<FormState> addSocialFormKey,
    required Function() onPress,
  }) async {
    return await showGeneralDialog(
      context: context,
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionDuration: const Duration(milliseconds: 400),
      transitionBuilder: (context, animation1, animation2, child) =>
          ScaleTransition(
            scale: animation1,
            child: AlertDialog(
              backgroundColor: Colors.white,
              contentPadding: EdgeInsets.all(15.0.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              title: const Center(child: Text("Add Social Link")),
              content: SingleChildScrollView(
                child: Form(
                  key: addSocialFormKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      addFormsWidget(
                        context: context,
                        title: "Search Social",
                        controller: searchSocialController,
                        validate: "Please Enter Search Social",
                      ),
                      SizedBox(height: 20.0.h),
                      addFormsWidget(
                        context: context,
                        title: "URL",
                        controller: urlController,
                        validate: "Please Enter URL",
                      ),

                      SizedBox(height: 20.0.h),
                      ElevatedButton(
                        onPressed: onPress,

                        style: ElevatedButton.styleFrom(
                          foregroundColor: Colors.white,
                          backgroundColor: AppColors.midLevelGreenColor,
                          textStyle: TextStyle(
                            fontSize: 16.0.sp,
                            fontWeight: FontWeight.bold,
                          ),
                          minimumSize: Size(398.0.w, 46.0.h),
                        ),
                        child: const Text("Save"),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
    );
  }

  static Future addLinkAlert({
    required BuildContext context,
    required TextEditingController linkNameController,
    required TextEditingController urlController,
    required GlobalKey<FormState> addLinkFormKey,
    required void Function() onPress,
  }) async {
    return await showGeneralDialog(
      context: context,
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionDuration: const Duration(milliseconds: 400),
      transitionBuilder: (context, animation1, animation2, child) =>
          ScaleTransition(
            scale: animation1,
            child: AlertDialog(
              backgroundColor: Colors.white,
              contentPadding: EdgeInsets.all(15.0.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
              title: const Center(child: Text("Add Link")),
              content: SingleChildScrollView(
                child: Form(
                  key: addLinkFormKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      addFormsWidget(
                        context: context,
                        title: "Link Name",
                        controller: linkNameController,
                        validate: "Please Enter Link Name",
                      ),
                      SizedBox(height: 20.0.h),
                      addFormsWidget(
                        context: context,
                        title: "URL",
                        controller: urlController,
                        validate: "Please Enter URL",
                      ),

                      SizedBox(height: 20.0.h),
                      ElevatedButton(
                        onPressed: onPress,

                        style: ElevatedButton.styleFrom(
                          foregroundColor: Colors.white,
                          backgroundColor: AppColors.midLevelGreenColor,
                          textStyle: TextStyle(
                            fontSize: 16.0.sp,
                            fontWeight: FontWeight.bold,
                          ),
                          minimumSize: Size(398.0.w, 46.0.h),
                        ),
                        child: const Text("Save"),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
    );
  }
}

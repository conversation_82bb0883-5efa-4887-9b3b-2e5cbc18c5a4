import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_details_speakers_model.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_sessions_model.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_speakers_model.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_time_slots_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class AgendaScreenWidgets {
  static Widget agendaheaderWidget({
    required BuildContext context,
    required TextEditingController controller,
    required List<AgendaSpeakersModel> speakersList,
    required List<AgendaTimeSlotsModel> timeSlotsList,
    required List<AgendaSessionsModel> sessionList,
    required void Function() onPress,
    required void Function(String)? onSubmitted,
  }) {
    return TextField(
      controller: controller,
      style: TextStyle(fontSize: 12.0.sp),
      onSubmitted: onSubmitted,
      decoration: InputDecoration(
        hintText: "Search",
        hintStyle: TextStyle(color: AppColors.greyColor, fontSize: 12.0.sp),
        border: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColors.greyColor),
          borderRadius: BorderRadius.all(Radius.circular(100.0.r)),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColors.greyColor),
          borderRadius: BorderRadius.all(Radius.circular(100.0.r)),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColors.greyColor),
          borderRadius: BorderRadius.all(Radius.circular(100.0.r)),
        ),
        disabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: AppColors.greyColor),
          borderRadius: BorderRadius.all(Radius.circular(100.0.r)),
        ),
        prefixIcon: const Icon(Icons.search),
        prefixIconColor: AppColors.greyColor,

        suffixIcon: InkWell(
          onTap: () {
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(32.0.r)),
              ),
              backgroundColor: Colors.white,
              builder: (context) {
                return DraggableScrollableSheet(
                  expand: false,
                  builder: (context, scrollController) {
                    return SingleChildScrollView(
                      controller: scrollController,
                      child: Padding(
                        padding: EdgeInsets.all(10.0.h),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Center(
                              child: Text(
                                "Filter",
                                style: TextStyle(
                                  fontSize: 20.0.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),

                            SizedBox(height: 10.0.h),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "Speakers",
                                  style: TextStyle(
                                    fontSize: 18.0.sp,
                                    color: AppColors.blackColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                InkWell(
                                  onTap: () {
                                    context
                                        .read(
                                          HomeScreenCategoriesProviders
                                              .agendaScreenProvidersApis,
                                        )
                                        .selectSpeaker(null);
                                  },
                                  child: Text(
                                    "Reset",
                                    style: TextStyle(
                                      fontSize: 16.0.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 5.0.h),
                            speakersListWithFilter(
                              speakersLength: speakersList.length,
                              speakers: speakersList,
                            ),
                            SizedBox(height: 10.0.h),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "Time",
                                  style: TextStyle(
                                    fontSize: 18.0.sp,
                                    color: AppColors.blackColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),

                                InkWell(
                                  onTap: () {
                                    context
                                        .read(
                                          HomeScreenCategoriesProviders
                                              .agendaScreenProvidersApis,
                                        )
                                        .selectTimeSlots(null);
                                  },
                                  child: Text(
                                    "Reset",
                                    style: TextStyle(
                                      fontSize: 16.0.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 5.0.h),
                            timeListWithFilter(
                              timeLength: timeSlotsList.length,
                              times: timeSlotsList,
                            ),
                            SizedBox(height: 10.0.h),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "Sessions",
                                  style: TextStyle(
                                    fontSize: 18.0.sp,
                                    color: AppColors.blackColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),

                                InkWell(
                                  onTap: () {
                                    context
                                        .read(
                                          HomeScreenCategoriesProviders
                                              .agendaScreenProvidersApis,
                                        )
                                        .selectSession(null);
                                  },
                                  child: Text(
                                    "Reset",
                                    style: TextStyle(
                                      fontSize: 16.0.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 5.0.h),
                            sessionsListWithFilter(
                              sessionLength: sessionList.length,
                              sessions: sessionList,
                            ),
                            SizedBox(height: 10.0.h),
                            ElevatedButton(
                              onPressed: onPress,
                              style: ElevatedButton.styleFrom(
                                foregroundColor: Colors.white,
                                backgroundColor: AppColors.midLevelGreenColor,
                                minimumSize: Size(398.0.w, 46.0.h),
                                textStyle: TextStyle(
                                  fontSize: 16.0.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              child: const Text("Show Results"),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            );
          },
          child: Container(
            margin: EdgeInsets.all(5.0.h),
            padding: EdgeInsets.all(10.0.h),
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.lightgreyColor,
            ),
            child: CommonComponents.imageAssetWithCache(
              context: context,
              image: AppImages.filterIcon,
              height: 20.0.h,
              width: 20.0.w,
              fit: BoxFit.contain,
            ),
          ),
        ),
      ),
    );
  }

  static Widget speakersListWithFilter({
    required int speakersLength,
    required List<AgendaSpeakersModel> speakers,
  }) {
    final StateProvider<bool> showReadMore = StateProvider((ref) => true);
    return Consumer(
      builder: (context, watch, child) => Column(
        children: [
          GridView.builder(
            physics: const BouncingScrollPhysics(),
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            itemCount: watch.watch(showReadMore)
                ? speakersLength >= 6
                      ? 6
                      : speakersLength
                : speakersLength,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisExtent: 40.0.h,
              crossAxisSpacing: 10.0.w,
              mainAxisSpacing: 10.0.h,
            ),
            itemBuilder: (context, index) => InkWell(
              onTap: () {
                context
                    .read(
                      HomeScreenCategoriesProviders.agendaScreenProvidersApis,
                    )
                    .selectSpeaker(speakers[index]);
              },
              child: Container(
                padding: EdgeInsets.all(10.0.h),
                height: 40.0.h,
                width: 132.0.w,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color:
                      watch
                              .watch(
                                HomeScreenCategoriesProviders
                                    .agendaScreenProvidersApis,
                              )
                              .getAgendaBySpeaker ==
                          speakers[index]
                      ? AppColors.darkGreenColor
                      : AppColors.lightgreyColor,
                  borderRadius: BorderRadius.all(Radius.circular(100.0.r)),
                ),
                child: Text(
                  speakers[index].speakerName!,
                  style: TextStyle(
                    fontSize: 14.0.sp,
                    fontWeight: FontWeight.bold,
                    color:
                        watch
                                .watch(
                                  HomeScreenCategoriesProviders
                                      .agendaScreenProvidersApis,
                                )
                                .getAgendaBySpeaker ==
                            speakers[index]
                        ? Colors.white
                        : AppColors.blackColor,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: 10.0.h),
          Visibility(
            visible: speakersLength > 6 && watch.watch(showReadMore),
            child: Align(
              alignment: Alignment.centerRight,
              child: InkWell(
                onTap: () {
                  context.read(showReadMore.notifier).state = false;
                },
                child: Text(
                  "Read More",
                  style: TextStyle(
                    fontSize: 14.0.sp,
                    color: AppColors.midLevelGreenColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Widget timeListWithFilter({
    required int timeLength,
    required List<AgendaTimeSlotsModel> times,
  }) {
    final StateProvider<bool> showReadMore = StateProvider((ref) => true);
    return Consumer(
      builder: (context, watch, child) => Column(
        children: [
          GridView.builder(
            physics: const BouncingScrollPhysics(),
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            itemCount: watch.watch(showReadMore)
                ? timeLength >= 6
                      ? 6
                      : timeLength
                : timeLength,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisExtent: 40.0.h,
              crossAxisSpacing: 10.0.w,
              mainAxisSpacing: 10.0.h,
            ),
            itemBuilder: (context, index) => InkWell(
              onTap: () {
                context
                    .read(
                      HomeScreenCategoriesProviders.agendaScreenProvidersApis,
                    )
                    .selectTimeSlots(times[index]);
              },
              child: Container(
                padding: EdgeInsets.all(10.0.h),
                height: 40.0.h,
                width: 132.0.w,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color:
                      watch
                              .watch(
                                HomeScreenCategoriesProviders
                                    .agendaScreenProvidersApis,
                              )
                              .getAgendaByTimeSlot ==
                          times[index]
                      ? AppColors.darkGreenColor
                      : AppColors.lightgreyColor,
                  borderRadius: BorderRadius.all(Radius.circular(100.0.r)),
                ),
                child: Text(
                  times[index].timeSlots!,
                  style: TextStyle(
                    fontSize: 14.0.sp,
                    fontWeight: FontWeight.bold,
                    color:
                        watch
                                .watch(
                                  HomeScreenCategoriesProviders
                                      .agendaScreenProvidersApis,
                                )
                                .getAgendaByTimeSlot ==
                            times[index]
                        ? Colors.white
                        : AppColors.blackColor,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: 10.0.h),
          Visibility(
            visible: timeLength > 6 && watch.watch(showReadMore),
            child: Align(
              alignment: Alignment.centerRight,
              child: InkWell(
                onTap: () {
                  context.read(showReadMore.notifier).state = false;
                },
                child: Text(
                  "Read More",
                  style: TextStyle(
                    fontSize: 14.0.sp,
                    color: AppColors.midLevelGreenColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Widget sessionsListWithFilter({
    required int sessionLength,
    required List<AgendaSessionsModel> sessions,
  }) {
    final StateProvider<bool> showReadMore = StateProvider((ref) => true);
    return Consumer(
      builder: (context, watch, child) => Column(
        children: [
          GridView.builder(
            physics: const BouncingScrollPhysics(),
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            itemCount: watch.watch(showReadMore)
                ? sessionLength >= 6
                      ? 6
                      : sessionLength
                : sessionLength,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisExtent: 40.0.h,
              crossAxisSpacing: 10.0.w,
              mainAxisSpacing: 10.0.h,
            ),
            itemBuilder: (context, index) => InkWell(
              onTap: () {
                context
                    .read(
                      HomeScreenCategoriesProviders.agendaScreenProvidersApis,
                    )
                    .selectSession(sessions[index]);
              },
              child: Container(
                padding: EdgeInsets.all(10.0.h),
                height: 40.0.h,
                width: 132.0.w,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color:
                      watch
                              .watch(
                                HomeScreenCategoriesProviders
                                    .agendaScreenProvidersApis,
                              )
                              .getAgendaBySession ==
                          sessions[index]
                      ? AppColors.darkGreenColor
                      : AppColors.lightgreyColor,
                  borderRadius: BorderRadius.all(Radius.circular(100.0.r)),
                ),
                child: Text(
                  sessions[index].sessionName!,
                  style: TextStyle(
                    fontSize: 14.0.sp,
                    fontWeight: FontWeight.bold,
                    color:
                        watch
                                .watch(
                                  HomeScreenCategoriesProviders
                                      .agendaScreenProvidersApis,
                                )
                                .getAgendaBySession ==
                            sessions[index]
                        ? Colors.white
                        : AppColors.blackColor,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: 10.0.h),
          Visibility(
            visible: sessionLength > 6 && watch.watch(showReadMore),
            child: Align(
              alignment: Alignment.centerRight,
              child: InkWell(
                onTap: () {
                  context.read(showReadMore.notifier).state = false;
                },
                child: Text(
                  "Read More",
                  style: TextStyle(
                    fontSize: 14.0.sp,
                    color: AppColors.midLevelGreenColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Widget bubleDaysWidget({
    required BuildContext context,
    required String title,
    required String subTitle,
    required Color textColor,
    required Color containerColor,
  }) {
    return Container(
      padding: EdgeInsets.all(15.0.h),
      alignment: Alignment.center,

      decoration: BoxDecoration(shape: BoxShape.circle, color: containerColor),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          Text(
            subTitle,
            style: TextStyle(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }

  static Widget sessionButtonsWidget({
    required BuildContext context,
    required String image,
    required Function() onPress,
  }) {
    return InkWell(
      onTap: onPress,
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.all(10.0.h),
        decoration: const BoxDecoration(
          color: AppColors.lightGreenColor,
          shape: BoxShape.circle,
        ),
        child: CommonComponents.imageAssetWithCache(
          context: context,
          image: image,
          height: 16.0.h,
          width: 16.0.w,
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  static Widget showSpeakersListWidget({
    required BuildContext context,
    required String image,

    required List<AgendaDetailsSpeakersModel> speakerList,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Speakers",
          style: TextStyle(
            fontSize: 12.0.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.greyColor,
          ),
        ),
        SizedBox(height: 5.0.h),
        SizedBox(
          height: 30.0.h,
          width: speakerList.length * 32.0.w,
          child: ListView.separated(
            separatorBuilder: (context, index) => SizedBox(width: 2.0.w),
            padding: EdgeInsets.zero,
            scrollDirection: Axis.horizontal,
            itemCount: speakerList.length,

            itemBuilder: (context, index) => InkWell(
              onTap: () {
                // Navigator.pushNamed(
                //   context,
                //   PATHS.agendaDetailsSpeakerScreen,
                //   arguments: AgendaDetailsSpeakerScreen(
                //     speakerId: speakerList[index].speakerID,
                //   ),
                // );
              },
              child: ClipOval(
                child: speakerList[index].speakerAvatar != null
                    ? CommonComponents.imageWithNetworkCache(
                        context: context,
                        image: speakerList[index].speakerAvatar!,
                        height: 24.0.h,
                        width: 24.0.w,
                        fit: BoxFit.contain,
                      )
                    : CommonComponents.imageAssetWithCache(
                        context: context,
                        image: AppImages.userPhoto,
                        height: 30.0.h,
                        width: 30.0.w,
                        fit: BoxFit.contain,
                      ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/routes.dart';

class ResourcesScreenWidgets {
  static List<Map<String, dynamic>> resourcesFileds = [
    {
      "title": "Attachments",
      "icon": AppImages.attachmentsIcon,
      "path": PATHS.attachmentScreen,
    },
    {
      "title": "Conf Papers",
      "icon": AppImages.attachmentsIcon,
      "path": PATHS.confPapersScreen,
    },
    {"title": "Floormap", "icon": AppImages.floormapIcon, "path": ""},
    {"title": "Logistics", "icon": AppImages.logisticsIcon, "path": ""},
    {
      "title": "Polls",
      "icon": AppImages.pollsSessionIcon,
      "path": PATHS.resourcesPollScreen,
    },
    {
      "title": "Images",
      "icon": AppImages.photosIcon,
      "path": PATHS.photosScreen,
    },
  ];
}

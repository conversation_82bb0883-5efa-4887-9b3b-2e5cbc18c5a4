import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AnnouncementScreenWidgets {
  static Future announcementAlert({
    required BuildContext context,
    required String title,
    required String subTitle,
  }) async {
    return await showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: "",
      pageBuilder: (context, animation1, animation2) => Container(),
      transitionDuration: const Duration(milliseconds: 400),
      transitionBuilder: (context, animation1, animation2, child) =>
          ScaleTransition(
            scale: animation1,
            child: AlertDialog(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),

              insetPadding: EdgeInsets.all(10.0.h),
              title: Text(
                "Announcement Details",
                style: TextStyle(
                  fontSize: 20.0.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.blackColor,
                ),
              ),
              content: Row(
                crossAxisAlignment: CrossAxisAlignment.start,

                children: [
                  Container(
                    padding: EdgeInsets.all(10.0.h),
                    decoration: const BoxDecoration(
                      color: AppColors.lightGreenColor,
                      shape: BoxShape.circle,
                    ),
                    child: CommonComponents.imageAssetWithCache(
                      context: context,
                      image: AppImages.announcementActiveIcon,
                      height: 24.0.h,
                      width: 24.0.w,
                      fit: BoxFit.contain,
                    ),
                  ),
                  SizedBox(width: 10.0.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            fontSize: 16.0.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.blackColor,
                          ),
                        ),

                        SizedBox(height: 10.0.h),
                        Text(
                          subTitle,
                          style: TextStyle(
                            fontSize: 12.0.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.greyColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }
}

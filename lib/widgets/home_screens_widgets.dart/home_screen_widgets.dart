import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HomeScreenWidgets {
  static Widget headerScreenWidget({
    required BuildContext context,
    required List<Map<String, dynamic>> eventTimer,
  }) {
    return Container(
      height: 254.0.h,
      width: 398.0.w,
      padding: EdgeInsets.all(12.0.h),
      decoration: BoxDecoration(
        color: AppColors.darkGreenColor,
        borderRadius: BorderRadius.all(Radius.circular(12.0.r)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,

        // mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CommonComponents.imageAssetWithCache(
            context: context,
            image: AppImages.logoImage,
            height: 56.0.h,
            width: 198.0.w,
            fit: BoxFit.contain,
          ),
          SizedBox(height: 15.0.h),
          Text(
            "37th AFA Int’l Technical Conference & Exhibition",
            style: TextStyle(
              fontSize: 18.0.sp,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 25.0.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: eventTimer
                .map(
                  (elements) => Row(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.center,

                        children: [
                          Text(
                            elements['time'].toString(),
                            style: TextStyle(
                              fontSize: 22.0.sp,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          SizedBox(height: 10.0.h),
                          Text(
                            elements['title'],
                            style: TextStyle(
                              fontSize: 12.0.sp,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                      elements['title'] != "Seconds"
                          ? Container(
                              margin: EdgeInsets.only(left: 20.0.w),
                              height: 52.0.h,
                              width: 2.0.w,
                              color: Colors.white,
                            )
                          : const SizedBox(),
                    ],
                  ),
                )
                .toList(),
          ),
        ],
      ),
    );
  }

  // static List<Map<String, dynamic>> eventTimer = [
  //   {"time": "-570", "title": "Days"},
  //   {"time": "-09", "title": "Hours"},
  //   {"time": "50", "title": "Minute"},
  //   {"time": "09", "title": "Seconds"},
  // ];

  static List<Map<String, dynamic>> homFiledsList = [
    {
      "title": "Agenda",
      "image": AppImages.agendaIcon,
      "path": PATHS.agendaScreen,
    },
    {
      "title": "Attendance",
      "image": AppImages.attendanceIcon,
      "path": PATHS.attendanceScreen,
    },
    {
      "title": "Speakers",
      "image": AppImages.speakersIcon,
      "path": PATHS.speakersScreen,
    },
    {
      "title": "Exibitors",
      "image": AppImages.exibitorsIcon,
      "path": PATHS.exibitorsScreen,
    },
    {
      "title": "Sponsors",
      "image": AppImages.sponsorsIcon,
      "path": PATHS.sponsorsScreen,
    },
    {
      "title": "Accomodation",
      "image": AppImages.venueIcon,
      "path": PATHS.accomodationScreen,
    },
    {
      "title": "Session Q&A",
      "image": AppImages.sessionIcon,
      "path": PATHS.sessionQuestionsScreen,
    },
    {
      "title": "Resources",
      "image": AppImages.resourcsesIcon,
      "path": PATHS.resourcesScreen,
    },
  ];
}

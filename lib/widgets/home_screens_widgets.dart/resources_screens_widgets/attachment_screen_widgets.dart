import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AttachmentScreenWidgets {
  static Widget bubleDaysWidget({
    required BuildContext context,
    required String title,
    required String subTitle,
    required Color textColor,
    required Color containerColor,
  }) {
    return Container(
      padding: EdgeInsets.all(15.0.h),
      alignment: Alignment.center,
      width: MediaQuery.of(context).size.width / 4.5,
      decoration: BoxDecoration(shape: BoxShape.circle, color: containerColor),
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 22.0.sp,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          Text(
            subTitle,
            style: TextStyle(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }
}

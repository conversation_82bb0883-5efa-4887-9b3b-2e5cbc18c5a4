import 'package:afa_app/app_config/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class LoginScreenWidgets {
  static Widget loginScreenFileds({
    required TextEditingController controller,
    required TextInputType type,
    required TextInputAction action,
    required String validate,
    required String hint,
    bool showPasswordIcon = false,
  }) {
    final StateProvider<bool> showPassword = StateProvider((ref) => true);
    return Consumer(
      builder: (context, watch, child) => TextFormField(
        controller: controller,
        autovalidateMode: AutovalidateMode.onUserInteraction,

        validator: (value) => value!.isEmpty ? validate : null,
        keyboardType: type,
        obscureText: showPasswordIcon ? watch.watch(showPassword) : false,
        style: TextStyle(fontSize: 12.0.sp),
        decoration: InputDecoration(
          hintText: hint,
          hintStyle: TextStyle(fontSize: 12.0.sp),
          errorStyle: TextStyle(fontSize: 14.0.sp),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            borderSide: const BorderSide(color: AppColors.greyColor),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            borderSide: const BorderSide(color: AppColors.greyColor),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            borderSide: const BorderSide(color: AppColors.greyColor),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            borderSide: const BorderSide(color: AppColors.greyColor),
          ),
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
            borderSide: const BorderSide(color: AppColors.greyColor),
          ),
          suffixIcon: Visibility(
            visible: showPasswordIcon,
            child: InkWell(
              onTap: () {
                context.read(showPassword.notifier).state = !context
                    .read(showPassword.notifier)
                    .state;
              },
              child: watch.watch(showPassword)
                  ? const Icon(Icons.visibility_off)
                  : const Icon(Icons.visibility),
            ),
          ),
          suffixIconColor: AppColors.midLevelGreenColor,
        ),
      ),
    );
  }
}

import 'package:afa_app/app_config/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ResetPasswordScreenWidgets {
  static Widget resetPasswordScreenFileds({
    required TextEditingController controller,
    required TextInputType type,
    required TextInputAction action,
    required String validate,
    required String hint,
  }) {
    return TextFormField(
      controller: controller,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (value) => value!.isEmpty ? validate : null,
      keyboardType: type,

      style: TextStyle(fontSize: 12.0.sp),
      decoration: InputDecoration(
        hintText: hint,
        hintStyle: TextStyle(fontSize: 12.0.sp),
        errorStyle: TextStyle(fontSize: 14.0.sp),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
          borderSide: const BorderSide(color: AppColors.greyColor),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
          borderSide: const BorderSide(color: AppColors.greyColor),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
          borderSide: const BorderSide(color: AppColors.greyColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
          borderSide: const BorderSide(color: AppColors.greyColor),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(8.0.r)),
          borderSide: const BorderSide(color: AppColors.greyColor),
        ),
      ),
    );
  }
}

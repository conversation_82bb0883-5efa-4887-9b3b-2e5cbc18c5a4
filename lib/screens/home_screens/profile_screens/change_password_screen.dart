import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/profile_screen_widgets/change_password_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final TextEditingController _oldPasswordController = TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _oldPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          padding: EdgeInsets.all(10.0.h),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CommonComponents.comonTitleScreen(
                  context: context,
                  title: "Change Password",
                ),
                SizedBox(height: 10.0.h),
                ChangePasswordScreenWidgets.changePasswordFileds(
                  title: "Old Password",
                  controller: _oldPasswordController,
                  type: TextInputType.visiblePassword,
                  action: TextInputAction.next,
                  validate: "Please Enter Old Password",
                  hint: "Enter",
                ),
                SizedBox(height: 10.0.h),
                ChangePasswordScreenWidgets.changePasswordFileds(
                  title: "New Password",
                  controller: _newPasswordController,
                  type: TextInputType.visiblePassword,
                  action: TextInputAction.next,
                  validate: "Please Enter New Password",
                  hint: "Enter",
                ),
                SizedBox(height: 10.0.h),
                ChangePasswordScreenWidgets.changePasswordFileds(
                  title: "Confirm Password",
                  controller: _confirmPasswordController,
                  type: TextInputType.visiblePassword,
                  action: TextInputAction.next,
                  validate: "Please Enter Confirm Password",
                  hint: "Enter",
                ),
                SizedBox(height: 20.0.h),
                ElevatedButton(
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {}
                  },
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: AppColors.midLevelGreenColor,
                    textStyle: TextStyle(
                      fontSize: 16.0.sp,
                      fontWeight: FontWeight.bold,
                    ),
                    minimumSize: Size(398.0.w, 46.0.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(Radius.circular(32.0.r)),
                    ),
                  ),
                  child: const Text("Save"),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

import 'dart:ui' as ui;
import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_providers/profile_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/profile_models/profile_badge_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:riverpod_context/riverpod_context.dart';
import 'package:saver_gallery/saver_gallery.dart';

class BadgeScreen extends StatefulWidget {
  const BadgeScreen({super.key});

  @override
  State<BadgeScreen> createState() => _BadgeScreenState();
}

class _BadgeScreenState extends State<BadgeScreen> {
  String? userImage, userName, userPosition;
  Future<ProfileBadgeModel>? _fetchProfileBadge;
  final GlobalKey _badgeKey = GlobalKey();

  @override
  void initState() {
    Future.delayed(Duration.zero, () async {
      final String name = await CommonComponents.getSavedData(ApiKeys.userName);
      final String image = await CommonComponents.getSavedData(
        ApiKeys.userImage,
      );
      final String position = await CommonComponents.getSavedData(
        ApiKeys.userPosition,
      );

      setState(() {
        userName = name;
        userImage = image;
        userPosition = position;
      });
    });

    _fetchProfileBadge = context
        .read(ProfileProviders.profileBadgeProvidersApis)
        .getProfileBadge(context: context);

    super.initState();
  }

  Future<void> _captureWidgetToImage() async {
    await [Permission.storage, Permission.photos].request();

    final boundary =
        _badgeKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
    final image = await boundary.toImage(pixelRatio: 3.0);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    final pngBytes = byteData!.buffer.asUint8List();

    await SaverGallery.saveImage(
      pngBytes,
      fileName: "Badge",
      skipIfExists: false,
    );
    if (!mounted) return;
    CommonComponents.showCustomizedSnackBar(
      context: context,
      title: "Image Saved Successfully",
    );
  }

  // Future<void> _captureAndSaveScreen() async {
  //   await [Permission.storage, Permission.photos].request();

  //   final RenderBox boundary =
  //       boundarykey.currentContext!.findRenderObject() as RenderBox;

  //   final ui.Image image = await _renderWidgetToImage(
  //     widget: RepaintBoundary(
  //       key: key,
  //       child: _buildContent(), // ده محتوى البادج اللي عايز تصوره
  //     ),
  //     logicalSize: renderBox.size,
  //     pixelRatio: pixelRatio,
  //   );

  //   final ByteData? byteData = await image.toByteData(
  //     format: ui.ImageByteFormat.png,
  //   );

  //   final Uint8List pngBytes = byteData!.buffer.asUint8List();

  //   await SaverGallery.saveImage(
  //     pngBytes,
  //     fileName: "Badge",
  //     skipIfExists: false,
  //   ).then((value) {
  //     if (!mounted) return;
  //     CommonComponents.showCustomizedSnackBar(
  //       context: context,
  //       title: "Image Saved Successfully",
  //     );
  //   });
  // }

  @override
  Widget build(BuildContext context) {
    if (userName == null || userImage == null || userPosition == null) {
      return CommonComponents.loadingDataFromServer();
    }

    return Scaffold(
      body: SafeArea(
        child: FutureBuilder(
          future: _fetchProfileBadge,
          builder: (context, AsyncSnapshot<ProfileBadgeModel> snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return CommonComponents.loadingDataFromServer();
            } else if (snapshot.data == null) {
              return CommonComponents.noDataFoundWidget();
            } else {
              return SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: EdgeInsets.all(10.0.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonComponents.comonTitleScreen(
                      context: context,
                      title: "Badge",
                    ),
                    SizedBox(height: 10.0.h),
                    RepaintBoundary(
                      key: _badgeKey,
                      child: Container(
                        color: Colors.white,
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.greyColor),
                            borderRadius: BorderRadius.all(
                              Radius.circular(20.0.r),
                            ),
                          ),
                          child: Column(
                            children: [
                              Padding(
                                padding: EdgeInsets.all(10.0.h),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        CommonComponents.imageAssetWithCache(
                                          context: context,
                                          image: AppImages.loginlogoImage,
                                          height: 50.0.h,
                                          width: 131.0.w,
                                          fit: BoxFit.contain,
                                        ),
                                        SizedBox(width: 20.0.w),
                                        Expanded(
                                          child: Text(
                                            snapshot.data!.eventTitle!,
                                            style: TextStyle(
                                              fontSize: 16.0.sp,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.blackColor,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 10.0.h),
                                    Row(
                                      children: [
                                        CommonComponents.imageAssetWithCache(
                                          context: context,
                                          image:
                                              AppImages.locationAboutEventIcon,
                                          height: 20.0.h,
                                          width: 16.0.w,
                                          fit: BoxFit.contain,
                                        ),
                                        SizedBox(width: 10.0.w),
                                        Expanded(
                                          child: Text(
                                            snapshot.data!.eventLocation!,
                                            style: TextStyle(
                                              fontSize: 12.0.sp,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 10.0.h),
                                    Row(
                                      children: [
                                        CommonComponents.imageAssetWithCache(
                                          context: context,
                                          image:
                                              AppImages.calendarAboutEventIcon,
                                          height: 20.0.h,
                                          width: 16.0.w,
                                          fit: BoxFit.contain,
                                        ),
                                        SizedBox(width: 10.0.w),
                                        Text(
                                          snapshot.data!.eventDate!,
                                          style: TextStyle(
                                            fontSize: 12.0.sp,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 10.0.h),
                                    const Divider(color: AppColors.greyColor),

                                    Center(
                                      child: ClipOval(
                                        child:
                                            CommonComponents.imageWithNetworkCache(
                                              context: context,
                                              image: userImage!,
                                              height: 104.0.h,
                                              width: 104.0.w,
                                              fit: BoxFit.cover,
                                            ),
                                      ),
                                    ),
                                    SizedBox(height: 10.0.h),
                                    Center(
                                      child: Text(
                                        userName!,
                                        style: TextStyle(
                                          fontSize: 20.0.sp,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.blackColor,
                                        ),
                                      ),
                                    ),
                                    Center(
                                      child: Text(
                                        userPosition!,
                                        style: TextStyle(
                                          fontSize: 16.0.sp,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.greyColor,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 10.0.h),
                                    Center(
                                      child: Text(
                                        snapshot.data!.company!,
                                        style: TextStyle(
                                          fontSize: 16.0.sp,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.greyColor,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 10.0.h),
                                    Center(
                                      child: Text(
                                        snapshot.data!.country!,
                                        style: TextStyle(
                                          fontSize: 16.0.sp,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.greyColor,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 10.0.h),
                                    // Center(
                                    //   child: Container(
                                    //     padding: EdgeInsets.all(10.0.h),
                                    //     decoration: BoxDecoration(
                                    //       color: const Color(0XFFFFECFA),
                                    //       borderRadius: BorderRadius.all(
                                    //         Radius.circular(16.0.r),
                                    //       ),
                                    //     ),
                                    //     child: Text(
                                    //       "Organizers",
                                    //       style: TextStyle(
                                    //         fontSize: 10.0.sp,
                                    //         color: const Color(0XFFED00BA),
                                    //       ),
                                    //     ),
                                    //   ),
                                    // ),
                                    SizedBox(height: 10.0.h),
                                    const Divider(color: AppColors.greyColor),
                                    Center(
                                      child:
                                          CommonComponents.imageWithNetworkCache(
                                            context: context,
                                            image: snapshot.data!.qrCode!,
                                            height: 120.0.h,
                                            width: 120.0.w,
                                            fit: BoxFit.contain,
                                          ),
                                    ),

                                    SizedBox(height: 10.0.h),
                                    Text(
                                      "Use this Badge To :",
                                      style: TextStyle(
                                        fontSize: 14.0.sp,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.blackColor,
                                      ),
                                    ),
                                    SizedBox(height: 10.0.h),
                                    ListView.separated(
                                      shrinkWrap: true,
                                      physics: const BouncingScrollPhysics(),
                                      separatorBuilder: (context, index) =>
                                          SizedBox(height: 10.0.h),
                                      itemCount:
                                          snapshot.data!.badgeUsage!.length,
                                      itemBuilder: (contetx, index) => Row(
                                        children: [
                                          CommonComponents.imageAssetWithCache(
                                            context: context,
                                            image: AppImages.checkIcon,
                                            height: 20.0.h,
                                            width: 20.0.w,
                                            fit: BoxFit.contain,
                                          ),
                                          SizedBox(width: 5.0.w),
                                          Text(
                                            snapshot.data!.badgeUsage![index],
                                            style: TextStyle(
                                              fontSize: 12.0.sp,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.blackColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 10.0.h),
                                    Text(
                                      "Thanks ,\nArab Fertilizer Association Team",
                                      style: TextStyle(
                                        fontSize: 16.0.sp,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    SizedBox(height: 15.0.h),
                                  ],
                                ),
                              ),
                              Container(
                                width: double.infinity,
                                alignment: Alignment.centerLeft,
                                padding: EdgeInsets.all(10.0.h),
                                decoration: BoxDecoration(
                                  color: AppColors.darkGreenColor,
                                  borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(20.0.r),
                                    bottomRight: Radius.circular(20.0.r),
                                  ),
                                ),
                                child: Column(
                                  children: [
                                    Text(
                                      "If you have any questions, contact us at\n${snapshot.data!.supportEmail}\n© 2025 Arab Fertilizer. All rights reserved.",
                                      style: TextStyle(
                                        fontSize: 14.0.sp,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 15.0.h),
                    ElevatedButton.icon(
                      onPressed: _captureWidgetToImage,

                      label: const Text("Download"),
                      icon: CommonComponents.imageAssetWithCache(
                        context: context,
                        image: AppImages.downloadIcon,
                        height: 24.0.h,
                        width: 24.0.w,
                        fit: BoxFit.contain,
                      ),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: AppColors.midLevelGreenColor,
                        minimumSize: Size(398.0.w, 48.0.h),
                        textStyle: TextStyle(
                          fontSize: 16.0.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    // SizedBox(height: 10.0.h),
                    // ElevatedButton.icon(
                    //   onPressed: () async {},
                    //   label: const Text("Print"),
                    //   icon: CommonComponents.imageAssetWithCache(
                    //     context: context,
                    //     image: AppImages.printIcon,
                    //     height: 24.0.h,
                    //     width: 24.0.w,
                    //     fit: BoxFit.contain,
                    //   ),
                    //   style: ElevatedButton.styleFrom(
                    //     foregroundColor: Colors.white,
                    //     backgroundColor: AppColors.darkGreenColor,
                    //     minimumSize: Size(398.0.w, 48.0.h),
                    //     textStyle: TextStyle(
                    //       fontSize: 16.0.sp,
                    //       fontWeight: FontWeight.bold,
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
              );
            }
          },
        ),
      ),
    );
  }
}

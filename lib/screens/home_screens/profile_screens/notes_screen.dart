import 'package:afa_app/app_config/api_providers/profile_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/profile_models/profile_notes_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class NotesScreen extends StatefulWidget {
  const NotesScreen({super.key});

  @override
  State<NotesScreen> createState() => _NotesScreenState();
}

class _NotesScreenState extends State<NotesScreen> {
  Future<List<ProfileNotesModel>>? _fetchProfileNotes;
  @override
  void initState() {
    _fetchProfileNotes = context
        .read(ProfileProviders.profileNotesProvidersApis)
        .getProfileNotes(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(10.0.h),
          child: FutureBuilder(
            future: _fetchProfileNotes,
            builder:
                (context, AsyncSnapshot<List<ProfileNotesModel>> snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return Center(
                      child: CommonComponents.loadingDataFromServer(),
                    );
                  } else if (snapshot.data == null) {
                    return Center(child: CommonComponents.noDataFoundWidget());
                  } else {
                    return Column(
                      children: [
                        CommonComponents.comonTitleScreen(
                          context: context,
                          title: "Notes",
                        ),
                        SizedBox(height: 10.0.h),
                        Expanded(
                          child: ListView.separated(
                            separatorBuilder: (context, index) =>
                                SizedBox(height: 10.0.h),
                            itemCount: 5,
                            itemBuilder: (context, index) => Container(
                              padding: EdgeInsets.all(10.0.h),

                              width: 398.0.w,
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: AppColors.lightGreenColor,
                                ),
                                borderRadius: BorderRadius.all(
                                  Radius.circular(16.0.r),
                                ),
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,

                                children: [
                                  Container(
                                    padding: EdgeInsets.all(10.0.h),
                                    decoration: const BoxDecoration(
                                      color: AppColors.lightGreenColor,
                                      shape: BoxShape.circle,
                                    ),
                                    child: CommonComponents.imageAssetWithCache(
                                      context: context,
                                      image: AppImages.noteIcon,
                                      height: 24.0.h,
                                      width: 24.0.w,
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                  SizedBox(width: 10.0.w),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          snapshot.data![index].note!,
                                          style: TextStyle(
                                            fontSize: 16.0.sp,
                                            fontWeight: FontWeight.bold,
                                            color: AppColors.blackColor,
                                          ),
                                        ),

                                        SizedBox(height: 10.0.h),
                                        Text(
                                          snapshot.data![index].date!,
                                          style: TextStyle(
                                            fontSize: 12.0.sp,
                                            fontWeight: FontWeight.bold,
                                            color: AppColors.greyColor,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  }
                },
          ),
        ),
      ),
    );
  }
}

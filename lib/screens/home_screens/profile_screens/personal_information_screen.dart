import 'package:afa_app/app_config/api_providers/profile_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/profile_models/profile_model.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/profile_screen_widgets/personal_information_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class PersonalInformationScreen extends StatefulWidget {
  const PersonalInformationScreen({super.key});

  @override
  State<PersonalInformationScreen> createState() =>
      _PersonalInformationScreenState();
}

class _PersonalInformationScreenState extends State<PersonalInformationScreen> {
  // final TextEditingController _companyNameController = TextEditingController();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _startDateAffiliationController =
      TextEditingController();
  final TextEditingController _endDateAffiliationController =
      TextEditingController();

  final TextEditingController _schoolNameController = TextEditingController();
  final TextEditingController _degreeController = TextEditingController();
  final TextEditingController _majorController = TextEditingController();
  final TextEditingController _startDateEducationController =
      TextEditingController();
  final TextEditingController _endDateEducationController =
      TextEditingController();

  // final TextEditingController _interestController = TextEditingController();
  final TextEditingController _linkNameWithOtheLinkController =
      TextEditingController();
  final TextEditingController _urlWithOtherLinkController =
      TextEditingController();

  final TextEditingController _socialNameController = TextEditingController();
  final TextEditingController _socialUrlController = TextEditingController();

  final _affiliationFormkey = GlobalKey<FormState>();
  final _educationFormkey = GlobalKey<FormState>();
  final _addLinkWithOtherLinkFormkey = GlobalKey<FormState>();
  final _addSocialFormkey = GlobalKey<FormState>();

  Future<ProfileModel>? _fetchProfileData;

  @override
  void initState() {
    _fetchProfileData = context
        .read(ProfileProviders.profilePersonalInformationProvidersApis)
        .getProfileData(context: context);
    super.initState();
  }

  @override
  void dispose() {
    // _companyNameController.dispose();
    _titleController.dispose();
    _startDateAffiliationController.dispose();
    _endDateAffiliationController.dispose();
    _schoolNameController.dispose();
    _degreeController.dispose();
    _majorController.dispose();
    _startDateEducationController.dispose();
    _endDateEducationController.dispose();
    // _interestController.dispose();
    _linkNameWithOtheLinkController.dispose();
    _urlWithOtherLinkController.dispose();
    _socialNameController.dispose();
    _socialUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Consumer(
          builder: (context, watch, child) => FutureBuilder(
            key: watch
                .watch(ProfileProviders.profilePersonalInformationProvidersApis)
                .profileKey,
            future: _fetchProfileData,
            builder: (context, AsyncSnapshot<ProfileModel> snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(child: CommonComponents.loadingDataFromServer());
              } else if (snapshot.data == null) {
                return Center(child: CommonComponents.noDataFoundWidget());
              } else {
                return SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  padding: EdgeInsets.all(15.0.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CommonComponents.comonTitleScreen(
                        context: context,
                        title: "Personal Information",
                      ),
                      SizedBox(height: 10.0.h),
                      PersonalInformationScreenWidgets.profileFields(
                        context: context,
                        image: AppImages.expierenceIcon,
                        title: "Affiliation",

                        onPressAdd: () async {
                          _titleController.clear();
                          _startDateAffiliationController.clear();
                          _endDateAffiliationController.clear();
                          context
                              .read(
                                ProfileProviders
                                    .profilePersonalInformationProvidersApis,
                              )
                              .setCompanySelected(null);
                          await PersonalInformationScreenWidgets.addAffiliationAlert(
                            context: context,
                            titleController: _titleController,
                            startDateController:
                                _startDateAffiliationController,
                            endDateController: _endDateAffiliationController,
                            affiliationFormKey: _affiliationFormkey,
                            onPress: () async {
                              if (_affiliationFormkey.currentState!
                                  .validate()) {
                                await context
                                    .read(
                                      ProfileProviders
                                          .profilePersonalInformationProvidersApis,
                                    )
                                    .userCreateAffiliation(
                                      context: context,
                                      title: _titleController.text,
                                      endaDate:
                                          _endDateAffiliationController.text,

                                      startDate:
                                          _startDateAffiliationController.text,
                                    )
                                    .then((value) {
                                      if (!context.mounted) return;
                                      Navigator.pop(context);

                                      context
                                          .read(
                                            ProfileProviders
                                                .profilePersonalInformationProvidersApis,
                                          )
                                          .rebuildProfileData();

                                      _fetchProfileData = context
                                          .read(
                                            ProfileProviders
                                                .profilePersonalInformationProvidersApis,
                                          )
                                          .getProfileData(context: context);
                                    });
                              }
                            },
                          );
                        },
                      ),
                      ListView.separated(
                        shrinkWrap: true,
                        separatorBuilder: (context, index) =>
                            SizedBox(height: 10.0.h),
                        itemCount: snapshot.data!.userAffiliations!.length,
                        itemBuilder: (context, index) => Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Text(
                                  snapshot
                                      .data!
                                      .userAffiliations![index]
                                      .title!,
                                  style: TextStyle(
                                    fontSize: 16.0.sp,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.blackColor,
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                InkWell(
                                  onTap: () async {
                                    await context
                                        .read(
                                          ProfileProviders
                                              .profilePersonalInformationProvidersApis,
                                        )
                                        .deleteUserAffilation(
                                          context: context,
                                          index: index,
                                        )
                                        .then((value) {
                                          if (!context.mounted) return;
                                          context
                                              .read(
                                                ProfileProviders
                                                    .profilePersonalInformationProvidersApis,
                                              )
                                              .rebuildProfileData();
                                          _fetchProfileData = context
                                              .read(
                                                ProfileProviders
                                                    .profilePersonalInformationProvidersApis,
                                              )
                                              .getProfileData(context: context);
                                        });
                                  },
                                  child: Container(
                                    padding: EdgeInsets.all(10.0.h),
                                    decoration: const BoxDecoration(
                                      color: Color(0XFFFEE1DE),
                                      shape: BoxShape.circle,
                                    ),
                                    child: CommonComponents.imageAssetWithCache(
                                      context: context,
                                      image: AppImages.deleteIcon,
                                      height: 20.0.h,
                                      width: 20.0.w,
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 10.0.w),
                                InkWell(
                                  onTap: () async {
                                    _titleController.text = snapshot
                                        .data!
                                        .userAffiliations![index]
                                        .title!;

                                    _startDateAffiliationController.text =
                                        snapshot
                                            .data!
                                            .userAffiliations![index]
                                            .startdate!;

                                    _endDateAffiliationController.text =
                                        snapshot
                                            .data!
                                            .userAffiliations![index]
                                            .endDate!;

                                    context
                                        .read(
                                          ProfileProviders
                                              .profilePersonalInformationProvidersApis,
                                        )
                                        .setCompanySelected(
                                          snapshot
                                              .data!
                                              .userAffiliations![index]
                                              .company,
                                        );

                                    PersonalInformationScreenWidgets.addAffiliationAlert(
                                      context: context,
                                      affiliationFormKey: _affiliationFormkey,

                                      titleController: _titleController,
                                      endDateController:
                                          _endDateAffiliationController,
                                      startDateController:
                                          _startDateAffiliationController,
                                      onPress: () async {
                                        if (_affiliationFormkey.currentState!
                                            .validate()) {
                                          await context
                                              .read(
                                                ProfileProviders
                                                    .profilePersonalInformationProvidersApis,
                                              )
                                              .userEditAffiliation(
                                                context: context,
                                                title: _titleController.text,
                                                endaDate:
                                                    _endDateAffiliationController
                                                        .text,

                                                startDate:
                                                    _startDateAffiliationController
                                                        .text,

                                                index: index,
                                              )
                                              .then((value) {
                                                if (!context.mounted) return;
                                                Navigator.pop(context);

                                                context
                                                    .read(
                                                      ProfileProviders
                                                          .profilePersonalInformationProvidersApis,
                                                    )
                                                    .rebuildProfileData();

                                                _fetchProfileData = context
                                                    .read(
                                                      ProfileProviders
                                                          .profilePersonalInformationProvidersApis,
                                                    )
                                                    .getProfileData(
                                                      context: context,
                                                    );
                                              });
                                        }
                                      },
                                    );
                                  },
                                  child: Container(
                                    padding: EdgeInsets.all(10.0.h),
                                    decoration: const BoxDecoration(
                                      color: AppColors.lightGreenColor,
                                      shape: BoxShape.circle,
                                    ),
                                    child: CommonComponents.imageAssetWithCache(
                                      context: context,
                                      image: AppImages.editIcon,
                                      height: 20.0.h,
                                      width: 20.0.w,
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 10.0.h),
                      const Divider(color: AppColors.greyColor),
                      PersonalInformationScreenWidgets.profileFields(
                        context: context,
                        image: AppImages.educationIcon,
                        title: "Education",

                        onPressAdd: () async {
                          _schoolNameController.clear();
                          _majorController.clear();
                          _degreeController.clear();
                          _startDateEducationController.clear();
                          _endDateEducationController.clear();

                          await PersonalInformationScreenWidgets.addAndEditEduactionAlert(
                            context: context,
                            schoolNameController: _schoolNameController,
                            degreeController: _degreeController,
                            majorController: _majorController,
                            startDateController: _startDateEducationController,
                            endDateController: _endDateEducationController,
                            educationFormKey: _educationFormkey,

                            onPress: () async {
                              if (_educationFormkey.currentState!.validate()) {
                                await context
                                    .read(
                                      ProfileProviders
                                          .profilePersonalInformationProvidersApis,
                                    )
                                    .userCreateEducation(
                                      context: context,
                                      schoolName: _schoolNameController.text,
                                      degree: _degreeController.text,
                                      major: _majorController.text,
                                      startDate:
                                          _startDateEducationController.text,
                                      endDate: _endDateEducationController.text,
                                    )
                                    .then((value) {
                                      if (!context.mounted) return;
                                      Navigator.pop(context);

                                      context
                                          .read(
                                            ProfileProviders
                                                .profilePersonalInformationProvidersApis,
                                          )
                                          .rebuildProfileData();

                                      _fetchProfileData = context
                                          .read(
                                            ProfileProviders
                                                .profilePersonalInformationProvidersApis,
                                          )
                                          .getProfileData(context: context);
                                    });
                              }
                            },
                          );
                        },
                      ),
                      ListView.separated(
                        shrinkWrap: true,
                        separatorBuilder: (context, index) =>
                            SizedBox(height: 10.0.h),
                        itemCount: snapshot.data!.userEduactions!.length,
                        itemBuilder: (context, index) => Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Text(
                                  snapshot
                                      .data!
                                      .userEduactions![index]
                                      .schoolName!,
                                  style: TextStyle(
                                    fontSize: 16.0.sp,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.blackColor,
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                InkWell(
                                  onTap: () async {
                                    await context
                                        .read(
                                          ProfileProviders
                                              .profilePersonalInformationProvidersApis,
                                        )
                                        .deleteUserEducation(
                                          context: context,
                                          index: index,
                                        )
                                        .then((value) {
                                          if (!context.mounted) return;
                                          context
                                              .read(
                                                ProfileProviders
                                                    .profilePersonalInformationProvidersApis,
                                              )
                                              .rebuildProfileData();
                                          _fetchProfileData = context
                                              .read(
                                                ProfileProviders
                                                    .profilePersonalInformationProvidersApis,
                                              )
                                              .getProfileData(context: context);
                                        });
                                  },
                                  child: Container(
                                    padding: EdgeInsets.all(10.0.h),
                                    decoration: const BoxDecoration(
                                      color: Color(0XFFFEE1DE),
                                      shape: BoxShape.circle,
                                    ),
                                    child: CommonComponents.imageAssetWithCache(
                                      context: context,
                                      image: AppImages.deleteIcon,
                                      height: 20.0.h,
                                      width: 20.0.w,
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 10.0.w),
                                InkWell(
                                  onTap: () async {
                                    _schoolNameController.text = snapshot
                                        .data!
                                        .userEduactions![index]
                                        .schoolName!;

                                    _degreeController.text = snapshot
                                        .data!
                                        .userEduactions![index]
                                        .degree!;

                                    _majorController.text = snapshot
                                        .data!
                                        .userEduactions![index]
                                        .major!;

                                    _startDateEducationController.text =
                                        snapshot
                                            .data!
                                            .userEduactions![index]
                                            .schoolStartDate!;

                                    _endDateEducationController.text = snapshot
                                        .data!
                                        .userEduactions![index]
                                        .schoolEndDate!;

                                    PersonalInformationScreenWidgets.addAndEditEduactionAlert(
                                      context: context,
                                      schoolNameController:
                                          _schoolNameController,
                                      degreeController: _degreeController,
                                      educationFormKey: _educationFormkey,
                                      majorController: _majorController,
                                      endDateController:
                                          _endDateEducationController,
                                      startDateController:
                                          _startDateEducationController,
                                      onPress: () async {
                                        if (_educationFormkey.currentState!
                                            .validate()) {
                                          await context
                                              .read(
                                                ProfileProviders
                                                    .profilePersonalInformationProvidersApis,
                                              )
                                              .userEditEducation(
                                                context: context,
                                                schoolName:
                                                    _schoolNameController.text,
                                                degree: _degreeController.text,
                                                major: _majorController.text,
                                                startDate:
                                                    _startDateEducationController
                                                        .text,
                                                endDate:
                                                    _endDateEducationController
                                                        .text,
                                                index: index,
                                              )
                                              .then((value) {
                                                if (!context.mounted) return;
                                                Navigator.pop(context);

                                                context
                                                    .read(
                                                      ProfileProviders
                                                          .profilePersonalInformationProvidersApis,
                                                    )
                                                    .rebuildProfileData();

                                                _fetchProfileData = context
                                                    .read(
                                                      ProfileProviders
                                                          .profilePersonalInformationProvidersApis,
                                                    )
                                                    .getProfileData(
                                                      context: context,
                                                    );
                                              });
                                        }
                                      },
                                    );
                                  },
                                  child: Container(
                                    padding: EdgeInsets.all(10.0.h),
                                    decoration: const BoxDecoration(
                                      color: AppColors.lightGreenColor,
                                      shape: BoxShape.circle,
                                    ),
                                    child: CommonComponents.imageAssetWithCache(
                                      context: context,
                                      image: AppImages.editIcon,
                                      height: 20.0.h,
                                      width: 20.0.w,
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 10.0.h),
                      const Divider(color: AppColors.greyColor),
                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //   children: [
                      //     Row(
                      //       children: [
                      //         CommonComponents.imageAssetWithCache(
                      //           context: context,
                      //           image: AppImages.interestIcon,
                      //           height: 24.0.h,
                      //           width: 24.0.w,
                      //           fit: BoxFit.contain,
                      //         ),
                      //         SizedBox(width: 5.0.w),
                      //         Text(
                      //           "Interests",
                      //           style: TextStyle(
                      //             fontSize: 18.0.sp,
                      //             color: AppColors.blackColor,
                      //             fontWeight: FontWeight.bold,
                      //           ),
                      //         ),
                      //       ],
                      //     ),
                      //     InkWell(
                      //       onTap: () async {
                      //         await PersonalInformationScreenWidgets.addInterestAlert(
                      //           context: context,
                      //           interestController: _interestController,
                      //         );
                      //       },
                      //       child: Container(
                      //         padding: EdgeInsets.all(10.0.h),
                      //         decoration: const BoxDecoration(
                      //           shape: BoxShape.circle,
                      //           color: AppColors.lightGreenColor,
                      //         ),

                      //         child: CommonComponents.imageAssetWithCache(
                      //           context: context,
                      //           image: AppImages.editIcon,
                      //           height: 20.0.h,
                      //           width: 20.0.w,
                      //           fit: BoxFit.contain,
                      //         ),
                      //       ),
                      //     ),
                      //   ],
                      // ),
                      // SizedBox(height: 10.0.h),
                      // GridView.builder(
                      //   physics: const BouncingScrollPhysics(),
                      //   shrinkWrap: true,
                      //   itemCount: 9,
                      //   gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      //     crossAxisCount: 4,
                      //     mainAxisExtent: 50.0.h,
                      //     crossAxisSpacing: 10.0.w,
                      //     mainAxisSpacing: 10.0.h,
                      //   ),
                      //   itemBuilder: (context, index) => Container(
                      //     alignment: Alignment.center,
                      //     padding: EdgeInsets.all(10.0.h),
                      //     height: 40.0.h,
                      //     width: 80.0.w,

                      //     decoration: BoxDecoration(
                      //       borderRadius: BorderRadius.all(Radius.circular(24.0.r)),
                      //       color: AppColors.lightgreyColor,
                      //     ),
                      //     child: Text(
                      //       "Travel",
                      //       style: TextStyle(
                      //         fontSize: 14.0.sp,
                      //         fontWeight: FontWeight.bold,
                      //       ),
                      //     ),
                      //   ),
                      // ),
                      // SizedBox(height: 10.0.h),
                      // const Divider(color: AppColors.greyColor),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              CommonComponents.imageAssetWithCache(
                                context: context,
                                image: AppImages.linksIcon,
                                height: 24.0.h,
                                width: 24.0.w,
                                fit: BoxFit.contain,
                              ),
                              SizedBox(width: 10.0.w),
                              Text(
                                "Social Links",
                                style: TextStyle(
                                  fontSize: 18.0.sp,
                                  color: AppColors.blackColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          InkWell(
                            onTap: () async {
                              _socialNameController.clear();
                              _socialUrlController.clear();

                              await PersonalInformationScreenWidgets.addAndEditSocialLinkAlert(
                                context: context,
                                searchSocialController: _socialNameController,
                                urlController: _socialUrlController,
                                addSocialFormKey: _addSocialFormkey,
                                onPress: () async {
                                  if (_addSocialFormkey.currentState!
                                      .validate()) {
                                    await context
                                        .read(
                                          ProfileProviders
                                              .profilePersonalInformationProvidersApis,
                                        )
                                        .addSocialMediaLink(
                                          context: context,
                                          linkName: _socialNameController.text,
                                          linkurl: _socialUrlController.text,
                                        )
                                        .then((value) {
                                          if (!context.mounted) return;
                                          Navigator.pop(context);
                                          context
                                              .read(
                                                ProfileProviders
                                                    .profilePersonalInformationProvidersApis,
                                              )
                                              .rebuildProfileData();
                                          _fetchProfileData = context
                                              .read(
                                                ProfileProviders
                                                    .profilePersonalInformationProvidersApis,
                                              )
                                              .getProfileData(context: context);
                                        });
                                  }
                                },
                              );
                            },
                            child: Container(
                              decoration: const BoxDecoration(
                                color: AppColors.lightGreenColor,
                                shape: BoxShape.circle,
                              ),
                              padding: EdgeInsets.all(10.0.h),
                              child: CommonComponents.imageAssetWithCache(
                                context: context,
                                image: AppImages.addFileIcon,
                                height: 20.0.h,
                                width: 20.0.w,
                                fit: BoxFit.contain,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10.0.h),
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const BouncingScrollPhysics(),
                        separatorBuilder: (context, index) =>
                            SizedBox(height: 10.0.h),
                        itemCount: snapshot.data!.userSocialLinks!.length,
                        itemBuilder: (context, index) => Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                CommonComponents.imageAssetWithCache(
                                  context: context,
                                  image: AppImages.shareRoundIcon,
                                  height: 24.0.h,
                                  width: 24.0.w,
                                  fit: BoxFit.contain,
                                ),
                                SizedBox(width: 10.0.w),
                                Text(
                                  snapshot
                                      .data!
                                      .userSocialLinks![index]
                                      .socialMediaName!,
                                  style: TextStyle(
                                    fontSize: 16.0.sp,
                                    color: AppColors.blackColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                InkWell(
                                  onTap: () async {
                                    await context
                                        .read(
                                          ProfileProviders
                                              .profilePersonalInformationProvidersApis,
                                        )
                                        .deleteSocialMediaLink(
                                          context: context,
                                          index: index,
                                        )
                                        .then((value) {
                                          if (!context.mounted) return;
                                          context
                                              .read(
                                                ProfileProviders
                                                    .profilePersonalInformationProvidersApis,
                                              )
                                              .rebuildProfileData();

                                          _fetchProfileData = context
                                              .read(
                                                ProfileProviders
                                                    .profilePersonalInformationProvidersApis,
                                              )
                                              .getProfileData(context: context);
                                        });
                                  },
                                  child: Container(
                                    decoration: const BoxDecoration(
                                      color: Color(0XFFFEE1DE),
                                      shape: BoxShape.circle,
                                    ),
                                    padding: EdgeInsets.all(10.0.h),
                                    child: CommonComponents.imageAssetWithCache(
                                      context: context,
                                      image: AppImages.deleteIcon,
                                      height: 20.0.h,
                                      width: 20.0.w,
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 10.0.w),
                                InkWell(
                                  onTap: () async {
                                    _socialNameController.text = snapshot
                                        .data!
                                        .userSocialLinks![index]
                                        .socialMediaName!;

                                    _socialUrlController.text = snapshot
                                        .data!
                                        .userSocialLinks![index]
                                        .socialMediaUrl!;
                                    await PersonalInformationScreenWidgets.addAndEditSocialLinkAlert(
                                      context: context,
                                      searchSocialController:
                                          _socialNameController,
                                      urlController: _socialUrlController,
                                      addSocialFormKey: _addSocialFormkey,
                                      onPress: () async {
                                        if (_addSocialFormkey.currentState!
                                            .validate()) {
                                          await context
                                              .read(
                                                ProfileProviders
                                                    .profilePersonalInformationProvidersApis,
                                              )
                                              .editSocialMediaLink(
                                                context: context,
                                                linkName:
                                                    _socialNameController.text,
                                                linkurl:
                                                    _socialUrlController.text,
                                                index: index,
                                              )
                                              .then((value) {
                                                if (!context.mounted) return;
                                                Navigator.pop(context);
                                                context
                                                    .read(
                                                      ProfileProviders
                                                          .profilePersonalInformationProvidersApis,
                                                    )
                                                    .rebuildProfileData();
                                                _fetchProfileData = context
                                                    .read(
                                                      ProfileProviders
                                                          .profilePersonalInformationProvidersApis,
                                                    )
                                                    .getProfileData(
                                                      context: context,
                                                    );
                                              });
                                        }
                                      },
                                    );
                                  },
                                  child: Container(
                                    decoration: const BoxDecoration(
                                      color: AppColors.lightGreenColor,
                                      shape: BoxShape.circle,
                                    ),
                                    padding: EdgeInsets.all(10.0.h),
                                    child: CommonComponents.imageAssetWithCache(
                                      context: context,
                                      image: AppImages.editIcon,
                                      height: 20.0.h,
                                      width: 20.0.w,
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 10.0.w),
                                InkWell(
                                  onTap: () async {
                                    await CommonComponents.launchOnBrowser(
                                      context: context,
                                      url: snapshot
                                          .data!
                                          .userSocialLinks![index]
                                          .socialMediaUrl!,
                                    );
                                  },
                                  child: Container(
                                    decoration: const BoxDecoration(
                                      color: AppColors.lightGreenColor,
                                      shape: BoxShape.circle,
                                    ),
                                    padding: EdgeInsets.all(10.0.h),
                                    child: CommonComponents.imageAssetWithCache(
                                      context: context,
                                      image: AppImages.shareIcon,
                                      height: 20.0.h,
                                      width: 20.0.w,
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 10.0.h),
                      const Divider(color: AppColors.greyColor),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              CommonComponents.imageAssetWithCache(
                                context: context,
                                image: AppImages.linksIcon,
                                height: 24.0.h,
                                width: 24.0.w,
                                fit: BoxFit.contain,
                              ),
                              SizedBox(width: 10.0.w),
                              Text(
                                "Other Links",
                                style: TextStyle(
                                  fontSize: 18.0.sp,
                                  color: AppColors.blackColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          InkWell(
                            onTap: () async {
                              _linkNameWithOtheLinkController.clear();
                              _urlWithOtherLinkController.clear();

                              await PersonalInformationScreenWidgets.addLinkAlert(
                                context: context,
                                linkNameController:
                                    _linkNameWithOtheLinkController,
                                urlController: _urlWithOtherLinkController,
                                addLinkFormKey: _addLinkWithOtherLinkFormkey,
                                onPress: () async {
                                  if (_addLinkWithOtherLinkFormkey.currentState!
                                      .validate()) {
                                    await context
                                        .read(
                                          ProfileProviders
                                              .profilePersonalInformationProvidersApis,
                                        )
                                        .addUserLink(
                                          context: context,
                                          linkName:
                                              _linkNameWithOtheLinkController
                                                  .text,
                                          linkUrl:
                                              _urlWithOtherLinkController.text,
                                        )
                                        .then((value) {
                                          if (!context.mounted) return;
                                          Navigator.pop(context);
                                          context
                                              .read(
                                                ProfileProviders
                                                    .profilePersonalInformationProvidersApis,
                                              )
                                              .rebuildProfileData();
                                          _fetchProfileData = context
                                              .read(
                                                ProfileProviders
                                                    .profilePersonalInformationProvidersApis,
                                              )
                                              .getProfileData(context: context);
                                        });
                                  }
                                },
                              );
                            },
                            child: Container(
                              decoration: const BoxDecoration(
                                color: AppColors.lightGreenColor,
                                shape: BoxShape.circle,
                              ),
                              padding: EdgeInsets.all(10.0.h),
                              child: CommonComponents.imageAssetWithCache(
                                context: context,
                                image: AppImages.addFileIcon,
                                height: 20.0.h,
                                width: 20.0.w,
                                fit: BoxFit.contain,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10.0.h),
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const BouncingScrollPhysics(),
                        separatorBuilder: (context, index) =>
                            SizedBox(height: 10.0.h),
                        itemCount: snapshot.data!.userLinks!.length,
                        itemBuilder: (context, index) => Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                CommonComponents.imageAssetWithCache(
                                  context: context,
                                  image: AppImages.shareRoundIcon,
                                  height: 24.0.h,
                                  width: 24.0.w,
                                  fit: BoxFit.contain,
                                ),
                                SizedBox(width: 10.0.w),
                                Text(
                                  snapshot.data!.userLinks![index].linkName!,
                                  style: TextStyle(
                                    fontSize: 16.0.sp,
                                    color: AppColors.blackColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                InkWell(
                                  onTap: () async {
                                    await context
                                        .read(
                                          ProfileProviders
                                              .profilePersonalInformationProvidersApis,
                                        )
                                        .deleteUserLink(
                                          context: context,
                                          index: index,
                                        )
                                        .then((value) {
                                          if (!context.mounted) return;
                                          context
                                              .read(
                                                ProfileProviders
                                                    .profilePersonalInformationProvidersApis,
                                              )
                                              .rebuildProfileData();

                                          _fetchProfileData = context
                                              .read(
                                                ProfileProviders
                                                    .profilePersonalInformationProvidersApis,
                                              )
                                              .getProfileData(context: context);
                                        });
                                  },
                                  child: Container(
                                    decoration: const BoxDecoration(
                                      color: Color(0XFFFEE1DE),
                                      shape: BoxShape.circle,
                                    ),
                                    padding: EdgeInsets.all(10.0.h),
                                    child: CommonComponents.imageAssetWithCache(
                                      context: context,
                                      image: AppImages.deleteIcon,
                                      height: 20.0.h,
                                      width: 20.0.w,
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 10.0.w),
                                InkWell(
                                  onTap: () async {
                                    _linkNameWithOtheLinkController.text =
                                        snapshot
                                            .data!
                                            .userLinks![index]
                                            .linkName!;

                                    _urlWithOtherLinkController.text = snapshot
                                        .data!
                                        .userLinks![index]
                                        .linkUrl!;

                                    await PersonalInformationScreenWidgets.addLinkAlert(
                                      context: context,
                                      linkNameController:
                                          _linkNameWithOtheLinkController,
                                      urlController:
                                          _urlWithOtherLinkController,
                                      addLinkFormKey:
                                          _addLinkWithOtherLinkFormkey,
                                      onPress: () async {
                                        if (_addLinkWithOtherLinkFormkey
                                            .currentState!
                                            .validate()) {
                                          Navigator.pop(context);
                                          await context
                                              .read(
                                                ProfileProviders
                                                    .profilePersonalInformationProvidersApis,
                                              )
                                              .editUserLink(
                                                context: context,
                                                linkName:
                                                    _linkNameWithOtheLinkController
                                                        .text,
                                                linkUrl:
                                                    _urlWithOtherLinkController
                                                        .text,
                                                index: index,
                                              )
                                              .then((value) {
                                                if (!context.mounted) return;
                                                Navigator.pop(context);
                                                context
                                                    .read(
                                                      ProfileProviders
                                                          .profilePersonalInformationProvidersApis,
                                                    )
                                                    .rebuildProfileData();
                                                _fetchProfileData = context
                                                    .read(
                                                      ProfileProviders
                                                          .profilePersonalInformationProvidersApis,
                                                    )
                                                    .getProfileData(
                                                      context: context,
                                                    );
                                              });
                                        }
                                      },
                                    );
                                  },
                                  child: Container(
                                    decoration: const BoxDecoration(
                                      color: AppColors.lightGreenColor,
                                      shape: BoxShape.circle,
                                    ),
                                    padding: EdgeInsets.all(10.0.h),
                                    child: CommonComponents.imageAssetWithCache(
                                      context: context,
                                      image: AppImages.editIcon,
                                      height: 20.0.h,
                                      width: 20.0.w,
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 10.0.w),
                                InkWell(
                                  onTap: () async {
                                    await CommonComponents.launchOnBrowser(
                                      context: context,
                                      url: snapshot
                                          .data!
                                          .userLinks![index]
                                          .linkUrl!,
                                    );
                                  },
                                  child: Container(
                                    decoration: const BoxDecoration(
                                      color: AppColors.lightGreenColor,
                                      shape: BoxShape.circle,
                                    ),
                                    padding: EdgeInsets.all(10.0.h),
                                    child: CommonComponents.imageAssetWithCache(
                                      context: context,
                                      image: AppImages.shareIcon,
                                      height: 20.0.h,
                                      width: 20.0.w,
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }
            },
          ),
        ),
      ),
    );
  }
}

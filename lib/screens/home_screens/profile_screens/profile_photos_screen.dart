import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:afa_app/app_config/api_providers/profile_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/profile_models/profile_photos_model.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/profile_screen_widgets/photos_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:riverpod_context/riverpod_context.dart';
import 'package:saver_gallery/saver_gallery.dart';

class ProfilePhotosScreen extends StatefulWidget {
  const ProfilePhotosScreen({super.key});

  @override
  State<ProfilePhotosScreen> createState() => _ProfilePhotosScreenState();
}

class _ProfilePhotosScreenState extends State<ProfilePhotosScreen> {
  Future<List<ProfilePhotosModel>>? _fetchProfilePhotos;
  final RefreshController _refreshController = RefreshController(
    initialRefresh: false,
  );

  final GlobalKey boundarykey = GlobalKey();

  final StateProvider<File?> photoUploaded = StateProvider((ref) => null);

  @override
  void initState() {
    _fetchProfilePhotos = context
        .read(ProfileProviders.profilePhotosProviderApis)
        .getProfilePhotos(
          context: context,
          controller: _refreshController,
          initialLoading: true,
        );

    super.initState();
  }

  void _onLoadingPhotosPgination() async {
    await Future.delayed(Duration.zero, () async {
      if (!mounted) return;
      _fetchProfilePhotos = context
          .read(ProfileProviders.profilePhotosProviderApis)
          .getProfilePhotos(
            context: context,
            controller: _refreshController,
            isLoading: true,
          );
    });
    setState(() {});
  }

  void _onRefreshPhotosPgination() async {
    await Future.delayed(Duration.zero, () async {
      if (!mounted) return;
      _fetchProfilePhotos = context
          .read(ProfileProviders.profilePhotosProviderApis)
          .getProfilePhotos(
            context: context,
            controller: _refreshController,
            isRefresh: true,
          );
    });
    setState(() {});
  }

  Future<void> _savePhoto(int index) async {
    await [Permission.storage, Permission.photos].request();
    if (!mounted) return;
    final RenderRepaintBoundary boundary =
        context
                .read(ProfileProviders.profilePhotosProviderApis)
                .boundarykeys[index]
                .currentContext!
                .findRenderObject()
            as RenderRepaintBoundary;

    final ui.Image image = await boundary.toImage(pixelRatio: 3.0);

    final ByteData? byteData = await image.toByteData(
      format: ui.ImageByteFormat.png,
    );

    final Uint8List pngBytes = byteData!.buffer.asUint8List();

    await SaverGallery.saveImage(
      pngBytes,
      fileName: "Photo",
      skipIfExists: false,
    ).then((value) {
      if (!mounted) return;
      CommonComponents.showCustomizedSnackBar(
        context: context,
        title: "Image Saved Successfully",
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Consumer(
          builder: (context, watch, child) => SmartRefresher(
            controller: _refreshController,
            enablePullUp: true,
            enablePullDown: true,
            onLoading: _onLoadingPhotosPgination,
            onRefresh: _onRefreshPhotosPgination,
            child: FutureBuilder(
              key: watch
                  .watch(ProfileProviders.profilePhotosProviderApis)
                  .photosKey,
              future: _fetchProfilePhotos,
              builder: (context, AsyncSnapshot<List<ProfilePhotosModel>> snapshot) {
                if (snapshot.data == null) {
                  return Center(
                    child: CommonComponents.loadingDataFromServer(),
                  );
                } else {
                  return SingleChildScrollView(
                    padding: EdgeInsets.all(10.0.h),
                    physics: const BouncingScrollPhysics(),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CommonComponents.comonTitleScreen(
                              context: context,
                              title: "Photos",
                            ),
                            InkWell(
                              onTap: () {
                                context.read(photoUploaded.notifier).state =
                                    null;
                                PhotosScreenWidgets.sharePhotoAlert(
                                  context: context,
                                  photo: photoUploaded,
                                  onPress: () async {
                                    // print("object");
                                    if (context.read(photoUploaded) != null) {
                                      await context
                                          .read(
                                            ProfileProviders
                                                .profilePhotosProviderApis,
                                          )
                                          .uploadPhotoToServer(
                                            context: context,
                                            photo: context
                                                .read(photoUploaded.notifier)
                                                .state,
                                          );
                                      if (!context.mounted) return;
                                      Navigator.pop(context);
                                      context
                                          .read(
                                            ProfileProviders
                                                .profilePhotosProviderApis,
                                          )
                                          .addBoundaryKey();

                                      context
                                          .read(
                                            ProfileProviders
                                                .profilePhotosProviderApis,
                                          )
                                          .rebuildPhotosWidget();

                                      _onRefreshPhotosPgination();

                                      // _fetchProfilePhotos = context
                                      //     .read(
                                      //       ProfileProviders
                                      //           .profilePhotosProviderApis,
                                      //     )
                                      //     .getProfilePhotos(
                                      //       context: context,
                                      //       controller: _refreshController,
                                      //       initialLoading: true,
                                      //     );
                                    } else {
                                      Navigator.pop(context);
                                    }
                                  },
                                );
                              },
                              child: Container(
                                alignment: Alignment.center,
                                padding: EdgeInsets.all(10.0.h),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: AppColors.lightGreenColor,
                                  ),
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(24.0.r),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    CommonComponents.imageAssetWithCache(
                                      context: context,
                                      image: AppImages.cameraIcon,
                                      height: 16.0.h,
                                      width: 16.0.w,
                                      fit: BoxFit.contain,
                                    ),
                                    SizedBox(width: 10.0.w),
                                    Text(
                                      "Share a Photo",
                                      style: TextStyle(
                                        fontSize: 12.0.sp,
                                        color: AppColors.midLevelGreenColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 10.0.h),
                        ListView.separated(
                          shrinkWrap: true,
                          physics: const BouncingScrollPhysics(),
                          separatorBuilder: (context, index) =>
                              SizedBox(height: 10.0.h),
                          itemCount: snapshot.data!.length,
                          itemBuilder: (context, index) => Card(
                            color: Colors.white,
                            elevation: 5.0.h,
                            child: Padding(
                              padding: EdgeInsets.all(10.0.h),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      ClipOval(
                                        child:
                                            CommonComponents.imageWithNetworkCache(
                                              context: context,
                                              image: snapshot
                                                  .data![index]
                                                  .userPhoto!,
                                              height: 40.0.h,
                                              width: 40.0.w,
                                              fit: BoxFit.contain,
                                            ),
                                      ),
                                      SizedBox(width: 5.0.w),
                                      Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            snapshot.data![index].userName!,
                                            style: TextStyle(
                                              fontSize: 14.0.sp,
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.blackColor,
                                            ),
                                          ),
                                          SizedBox(height: 5.0.h),
                                          Text(
                                            snapshot.data![index].photoDate!,
                                            style: TextStyle(
                                              fontSize: 10.0.sp,
                                              color: AppColors.greyColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 10.0.h),
                                  ClipRRect(
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(16.0.r),
                                    ),
                                    child:
                                        snapshot.data![index].photoUrl != null
                                        ? RepaintBoundary(
                                            key: watch
                                                .watch(
                                                  ProfileProviders
                                                      .profilePhotosProviderApis,
                                                )
                                                .boundarykeys[index],
                                            child:
                                                CommonComponents.imageWithNetworkCache(
                                                  context: context,
                                                  image: snapshot
                                                      .data![index]
                                                      .photoUrl!,
                                                  height: 132.0.h,
                                                  width: 374.0.w,
                                                  fit: BoxFit.fill,
                                                ),
                                          )
                                        : CommonComponents.imageAssetWithCache(
                                            context: context,
                                            image: AppImages.logoTestImage,
                                            height: 132.0.h,
                                            width: 374.0.w,
                                            fit: BoxFit.fill,
                                          ),
                                  ),
                                  SizedBox(height: 10.0.h),
                                  ElevatedButton.icon(
                                    onPressed: () {
                                      _savePhoto(index);
                                    },
                                    style: ElevatedButton.styleFrom(
                                      foregroundColor:
                                          AppColors.midLevelGreenColor,
                                      backgroundColor: Colors.white,
                                      minimumSize: Size(93.0.w, 28.0.h),
                                      textStyle: TextStyle(
                                        fontSize: 12.0.sp,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(
                                          5.0.r,
                                        ),
                                      ),
                                    ),
                                    label: const Text("Download"),
                                    icon: CommonComponents.imageAssetWithCache(
                                      context: context,
                                      image: AppImages.downloadGreenIcon,
                                      height: 15.0.h,
                                      width: 15.0.w,
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }
              },
            ),
          ),
        ),
      ),
    );
  }
}

import 'dart:io';

import 'package:afa_app/app_config/api_providers/profile_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:afa_app/models/profile_models/user_countries_model.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/profile_screen_widgets.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/profile_screen_widgets/setting_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:riverpod_context/riverpod_context.dart';

class SettingScreen extends StatefulWidget {
  const SettingScreen({super.key});

  @override
  State<SettingScreen> createState() => _SettingScreenState();
}

class _SettingScreenState extends State<SettingScreen> {
  final TextEditingController _nameController = TextEditingController();
  // final TextEditingController _companyController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneNumberController = TextEditingController();
  final TextEditingController _positionController = TextEditingController();
  final TextEditingController _bioController = TextEditingController();
  String? userImage;
  // final TextEditingController _passwordController = TextEditingController();
  final _settingFormKey = GlobalKey<FormState>();
  // final _deleteAccountFormKey = GlobalKey<FormState>();

  // Future<ProfileUserDataModel>? _fetchUserData;

  final StateProvider<File?> _userImageChanged = StateProvider((ref) => null);

  @override
  void initState() {
    Future.delayed(Duration.zero, () async {
      if (!mounted) return;
      await context
          .read(ProfileProviders.profileSettingsProvidersApis)
          .getUserData(context: context, getMyProfileData: true)
          .then((value) {
            setState(() {
              userImage = value.userImage!;
              _nameController.text = value.userName!;
              _emailController.text = value.userEmail!;
              _phoneNumberController.text = value.phoneNumber!;
              _positionController.text = value.position!;
              _bioController.text = value.bio!;
              context
                  .read(ProfileProviders.profileSettingsProvidersApis)
                  .setSelectedCountry(
                    UserCountriesModel(
                      countryName: value.country,
                      countryValue: value.country,
                    ),
                  );
            });
          });
    });

    super.initState();
  }

  @override
  void dispose() {
    _nameController.dispose();
    // _companyController.dispose();
    _emailController.dispose();
    _phoneNumberController.dispose();
    _positionController.dispose();
    _bioController.dispose();
    // _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_nameController.text.isEmpty) {
      return Scaffold(
        body: Center(child: CommonComponents.loadingDataFromServer()),
      );
    }
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          padding: EdgeInsets.all(12.0.h),
          child: Form(
            key: _settingFormKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 20.0.h,
              children: [
                CommonComponents.comonTitleScreen(
                  context: context,
                  title: "Settings",
                ),
                SizedBox(height: 20.0.h),
                Center(
                  child: Stack(
                    children: [
                      Consumer(
                        builder: (context, watch, child) => ClipOval(
                          child: watch.watch(_userImageChanged) == null
                              ? CommonComponents.imageWithNetworkCache(
                                  context: context,
                                  image: userImage!,
                                  height: 104.0.h,
                                  width: 104.0.w,
                                  fit: BoxFit.contain,
                                )
                              : CommonComponents.imageFromFile(
                                  context: context,
                                  image: watch.watch(_userImageChanged)!,
                                  height: 104.0.h,
                                  width: 104.0.w,
                                  fit: BoxFit.contain,
                                ),
                        ),
                      ),
                      Positioned(
                        bottom: 10.0,
                        right: 0.0,
                        child: InkWell(
                          onTap: () async {
                            final File? image =
                                await CommonComponents.pickImage(
                                  context: context,
                                  source: ImageSource.gallery,
                                );

                            if (image != null) {
                              if (!context.mounted) return;
                              context.read(_userImageChanged.notifier).state =
                                  image;
                            }
                          },
                          child: Container(
                            padding: EdgeInsets.all(5.0.h),
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                              color: AppColors.darkGreenColor,
                            ),
                            child: Icon(
                              Icons.camera_alt_outlined,
                              color: Colors.white,
                              size: 17.0.h,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 20.0.h),
                SettingScreenWidgets.settingFiledsWidget(
                  title: "Name",
                  controller: _nameController,
                  validate: "Please Enter Name",
                  type: TextInputType.name,
                  action: TextInputAction.next,
                  hint: "Enter",
                ),
                // SettingScreenWidgets.settingFiledsWidget(
                //   title: "Company",
                //   controller: _companyController,
                //   validate: "Please Enter Company",
                //   type: TextInputType.name,
                //   action: TextInputAction.next,
                //   hint: "Enter",
                // ),
                SettingScreenWidgets.settingFiledsWidget(
                  title: "Email",
                  controller: _emailController,
                  validate: "Please Enter Email",
                  type: TextInputType.emailAddress,
                  action: TextInputAction.next,
                  hint: "Enter",
                ),
                SettingScreenWidgets.settingFiledsWidget(
                  title: "Phone Number",
                  controller: _phoneNumberController,
                  validate: "Please Enter Phone Number",
                  type: TextInputType.phone,
                  action: TextInputAction.next,
                  hint: "Enter",
                ),
                SettingScreenWidgets.settingFiledsWidget(
                  title: "Position",
                  controller: _positionController,
                  validate: "Please Enter Phone Position",
                  type: TextInputType.text,
                  action: TextInputAction.next,
                  hint: "Enter",
                ),
                Text(
                  "Country",
                  style: TextStyle(
                    fontSize: 14.0.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.blackColor,
                  ),
                ),
                SettingScreenWidgets.settingsFileldsDropDownWidget(
                  dataList: (countries) async => await context
                      .read(ProfileProviders.profileSettingsProvidersApis)
                      .getAllCountries(context: context),
                  selectedItem: context
                      .read(ProfileProviders.profileSettingsProvidersApis)
                      .selectedCountry,
                  items: (country) => country.countryName,
                  onChanged: (value) {
                    context
                        .read(ProfileProviders.profileSettingsProvidersApis)
                        .setSelectedCountry(value);
                  },
                ),
                SettingScreenWidgets.settingFiledsWidget(
                  title: "Bio",
                  controller: _bioController,
                  // validate: "Please Enter Phone Bio",
                  type: TextInputType.text,
                  action: TextInputAction.next,
                  hint: "Enter",
                  maxLines: 5,
                ),
                ElevatedButton(
                  onPressed: () async {
                    if (_settingFormKey.currentState!.validate()) {
                      await context
                          .read(ProfileProviders.profileSettingsProvidersApis)
                          .userEditProfile(
                            context: context,
                            userImage: context.read(_userImageChanged),
                            userName: _nameController.text,
                            phoneNumber: _phoneNumberController.text,
                            userEmail: _emailController.text,
                            position: _positionController.text,
                            bio: _bioController.text,
                            country: "country",
                          );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: AppColors.midLevelGreenColor,
                    minimumSize: Size(398.0.w, 46.0.h),
                    textStyle: TextStyle(
                      fontSize: 16.0.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  child: const Text("Save Changes"),
                ),

                ProfileScreenWidgets.profileFileds(
                  context: context,
                  image: AppImages.changePasswordIcon,
                  title: "Change Password",
                  onPress: () {
                    Navigator.pushNamed(context, PATHS.changePasswordScreen);
                  },
                ),
                // ProfileScreenWidgets.profileFileds(
                //   context: context,
                //   image: AppImages.deleteAccountIcon,
                //   title: "Delete Account",
                //   onPress: () async {
                //     await SettingScreenWidgets.deleteAccountAlert(
                //       context: context,
                //       passwordController: _passwordController,
                //       formKey: _deleteAccountFormKey,
                //     );
                //   },
                //   textColor: Colors.red,
                //   arrowColor: Colors.red,
                //   filedColor: const Color(0XFFFEE1DE),
                // ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

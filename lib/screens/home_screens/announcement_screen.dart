import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/announcements_screen_model.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/announcement_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class AnnouncementScreen extends StatefulWidget {
  const AnnouncementScreen({super.key});

  @override
  State<AnnouncementScreen> createState() => _AnnouncementScreenState();
}

class _AnnouncementScreenState extends State<AnnouncementScreen> {
  Future<List<AnnouncementsScreenModel>>? _fetchAnnouncements;

  @override
  void initState() {
    _fetchAnnouncements = context
        .read(HomeScreenCategoriesProviders.announcementsScreenProvidersApis)
        .getAnnouncements(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: _fetchAnnouncements,
      builder: (context, AsyncSnapshot<List<AnnouncementsScreenModel>> snapshot) {
        if (snapshot.data == null) {
          return Center(child: CommonComponents.loadingDataFromServer());
        } else {
          return ListView.separated(
            shrinkWrap: true,
            physics: const BouncingScrollPhysics(),
            separatorBuilder: (context, index) => SizedBox(height: 10.0.h),
            itemCount: snapshot.data!.length,
            itemBuilder: (context, index) => Container(
              padding: EdgeInsets.all(10.0.h),

              width: 398.0.w,
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.lightGreenColor),
                borderRadius: BorderRadius.all(Radius.circular(16.0.r)),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,

                children: [
                  Container(
                    padding: EdgeInsets.all(10.0.h),
                    decoration: const BoxDecoration(
                      color: AppColors.lightGreenColor,
                      shape: BoxShape.circle,
                    ),
                    child: CommonComponents.imageAssetWithCache(
                      context: context,
                      image: AppImages.announcementActiveIcon,
                      height: 24.0.h,
                      width: 24.0.w,
                      fit: BoxFit.contain,
                    ),
                  ),
                  SizedBox(width: 10.0.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          snapshot.data![index].title!,
                          style: TextStyle(
                            fontSize: 16.0.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.blackColor,
                          ),
                        ),
                        SizedBox(height: 10.0.h),
                        Text(
                          snapshot.data![index].content!,
                          style: TextStyle(
                            fontSize: 12.0.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.greyColor,
                          ),
                        ),
                        SizedBox(height: 10.0.h),
                        Text(
                          "${snapshot.data![index].date} at ${snapshot.data![index].time}",
                          style: TextStyle(
                            fontSize: 12.0.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.greyColor,
                          ),
                        ),
                        SizedBox(height: 10.0.h),
                        TextButton(
                          onPressed: () async {
                            await AnnouncementScreenWidgets.announcementAlert(
                              context: context,
                              title: snapshot.data![index].title!,
                              subTitle: snapshot.data![index].content!,
                            );
                          },
                          style: TextButton.styleFrom(
                            foregroundColor: AppColors.midLevelGreenColor,
                            textStyle: TextStyle(
                              fontSize: 12.0.sp,
                              decoration: TextDecoration.underline,
                              height: 0.5.h,
                              fontWeight: FontWeight.bold,
                            ),

                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            padding: const EdgeInsets.all(0.0),
                            minimumSize: Size(68.0.w, 17.0.h),
                          ),
                          child: const Text("View Details"),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        }
      },
    );
  }
}

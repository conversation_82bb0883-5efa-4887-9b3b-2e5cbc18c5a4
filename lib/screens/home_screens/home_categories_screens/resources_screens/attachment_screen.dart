import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/home_screens_models/resources_screens_models/attachment_screen_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class AttachmentScreen extends StatefulWidget {
  const AttachmentScreen({super.key});

  @override
  State<AttachmentScreen> createState() => _AttachmentScreenState();
}

class _AttachmentScreenState extends State<AttachmentScreen> {
  Future<List<AttachmentScreenModel>>? _fetchAttachmentsList;

  @override
  void initState() {
    _fetchAttachmentsList = context
        .read(HomeScreenCategoriesProviders.attachmentScreenProvidersApis)
        .getAttachmentsList(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: FutureBuilder(
          future: _fetchAttachmentsList,
          builder: (context, AsyncSnapshot<List<AttachmentScreenModel>> snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(child: CommonComponents.loadingDataFromServer());
            } else if (snapshot.data == null) {
              return Center(child: CommonComponents.noDataFoundWidget());
            } else {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonComponents.comonTitleScreen(
                    context: context,
                    title: "Attachments",
                  ),
                  SizedBox(height: 10.0.h),

                  // SizedBox(
                  //   height: 80.0.h,

                  //   child: ListView.separated(
                  //     physics: const BouncingScrollPhysics(),
                  //     scrollDirection: Axis.horizontal,
                  //     separatorBuilder: (context, index) =>
                  //         SizedBox(width: 10.0.w),
                  //     itemCount: 4,
                  //     itemBuilder: (context, index) =>
                  //         AttachmentScreenWidgets.bubleDaysWidget(
                  //           context: context,
                  //           title: index == 0
                  //               ? "16"
                  //               : index == 1
                  //               ? "17"
                  //               : index == 2
                  //               ? "18"
                  //               : "19",
                  //           subTitle: index == 0
                  //               ? "Day 0"
                  //               : index == 1
                  //               ? "Day 1"
                  //               : index == 2
                  //               ? "Day 2"
                  //               : "Day 3",
                  //           textColor: index == 0
                  //               ? AppColors.midLevelGreenColor
                  //               : AppColors.blackColor,
                  //           containerColor: index == 0
                  //               ? AppColors.darkGreenColor
                  //               : AppColors.lightgreyColor,
                  //         ),
                  //   ),
                  // ),
                  // SizedBox(height: 10.0.h),
                  SizedBox(height: 10.0.h),

                  Expanded(
                    child: ListView.separated(
                      padding: EdgeInsets.all(10.0.h),

                      physics: const BouncingScrollPhysics(),
                      separatorBuilder: (context, index) =>
                          SizedBox(height: 10.0.h),
                      itemCount: snapshot.data!.length,
                      itemBuilder: (context, index) => Container(
                        padding: EdgeInsets.all(10.0.h),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.greyColor),
                          borderRadius: BorderRadius.all(
                            Radius.circular(16.0.r),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "${snapshot.data![index].title}",
                              style: TextStyle(
                                fontSize: 16.0.sp,
                                fontWeight: FontWeight.bold,
                                color: AppColors.blackColor,
                              ),
                            ),

                            // SizedBox(height: 10.0.h),
                            // Row(
                            //   children: [
                            //     Icon(
                            //       Icons.alarm,
                            //       size: 15.0.h,
                            //       color: AppColors.midLevelGreenColor,
                            //     ),
                            //     Text(
                            //       snapshot.data![index].time!,
                            //       style: TextStyle(
                            //         fontSize: 12.0.sp,
                            //         color: AppColors.midLevelGreenColor,
                            //       ),
                            //     ),
                            //   ],
                            // ),
                            SizedBox(height: 20.0.h),
                            const Divider(color: AppColors.greyColor),
                            Row(
                              children: [
                                Row(
                                  children: [
                                    ElevatedButton.icon(
                                      onPressed: () async {
                                        await CommonComponents.launchOnBrowser(
                                          context: context,
                                          url: snapshot.data![index].pdfLink!,
                                        );
                                      },
                                      label: const Text("Download PDF"),
                                      icon:
                                          CommonComponents.imageAssetWithCache(
                                            context: context,
                                            image: AppImages.downloadGreenIcon,
                                            height: 20.0.h,
                                            width: 20.0.w,
                                            fit: BoxFit.cover,
                                          ),
                                      style: ElevatedButton.styleFrom(
                                        foregroundColor:
                                            AppColors.midLevelGreenColor,
                                        backgroundColor: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.all(
                                            Radius.circular(40.0.r),
                                          ),
                                          side: const BorderSide(
                                            color: AppColors.greyColor,
                                          ),
                                        ),
                                        minimumSize: Size(132.0.w, 40.0.h),
                                      ),
                                    ),
                                    SizedBox(width: 10.0.w),
                                    // ElevatedButton.icon(
                                    //   onPressed: () async {
                                    //     await CommonComponents.launchOnBrowser(
                                    //       context: context,
                                    //       url: snapshot.data![index].videoLink!,
                                    //     );
                                    //   },
                                    //   label: const Text("Watch Video"),
                                    //   icon:
                                    //       CommonComponents.imageAssetWithCache(
                                    //         context: context,
                                    //         image: AppImages.playVideoIcon,
                                    //         height: 20.0.h,
                                    //         width: 20.0.w,
                                    //         fit: BoxFit.cover,
                                    //       ),
                                    //   style: ElevatedButton.styleFrom(
                                    //     foregroundColor:
                                    //         AppColors.midLevelGreenColor,
                                    //     backgroundColor: Colors.white,
                                    //     shape: RoundedRectangleBorder(
                                    //       borderRadius: BorderRadius.all(
                                    //         Radius.circular(40.0.r),
                                    //       ),
                                    //       side: const BorderSide(
                                    //         color: AppColors.greyColor,
                                    //       ),
                                    //     ),
                                    //     minimumSize: Size(118.0.w, 40.0.h),
                                    //   ),
                                    // ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              );
            }
          },
        ),
      ),
    );
  }
}

import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/polls_screen_models/poll_options_model.dart';
import 'package:afa_app/models/polls_screen_models/polls_questions_model.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/polls_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';
import 'package:uid/uid.dart';

class ResourcesPollScreen extends StatefulWidget {
  const ResourcesPollScreen({super.key});

  @override
  State<ResourcesPollScreen> createState() => _ResourcesPollScreenState();
}

class _ResourcesPollScreenState extends State<ResourcesPollScreen> {
  final TextEditingController _questionController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  Future<List<PollsQuestionsModel>>? _fetchAllPolls;

  static List<TextEditingController> optionsControllers = [
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
  ];

  @override
  void initState() {
    _fetchAllPolls = context
        .read(HomeScreenCategoriesProviders.pollsScreenProvidersApis)
        .getAllPolls(context: context);
    super.initState();
  }

  @override
  void dispose() {
    _questionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          padding: EdgeInsets.all(10.0.h),
          child: Column(
            children: [
              CommonComponents.comonTitleScreen(
                context: context,
                title: "Polls",
              ),
              SizedBox(height: 10.0.h),
              Consumer(
                builder: (context, watch, child) => FutureBuilder(
                  key: watch
                      .watch(
                        HomeScreenCategoriesProviders.pollsScreenProvidersApis,
                      )
                      .pollsKey,
                  future: _fetchAllPolls,
                  builder:
                      (
                        context,
                        AsyncSnapshot<List<PollsQuestionsModel>> snapshot,
                      ) {
                        if (snapshot.connectionState ==
                            ConnectionState.waiting) {
                          return Center(
                            child: CommonComponents.loadingDataFromServer(),
                          );
                        } else if (snapshot.data == null) {
                          return Center(
                            child: CommonComponents.noDataFoundWidget(),
                          );
                        } else {
                          return PollsScreenWidgets.pollsWidget(
                            context: context,

                            formKey: _formKey,
                            questionController: _questionController,
                            pollsQuestions: snapshot.data!,
                            optionsController: optionsControllers,
                            onPress: () async {
                              if (_formKey.currentState!.validate()) {
                                await context
                                    .read(
                                      HomeScreenCategoriesProviders
                                          .pollsScreenProvidersApis,
                                    )
                                    .userCreatepoll(
                                      context: context,
                                      question: _questionController.text,
                                      optionsList: [
                                        PollOptionsModel(
                                          optionTitle:
                                              optionsControllers[0].text,
                                          optionID: UId.getId(),
                                          votesCount: 0,
                                        ),
                                        PollOptionsModel(
                                          optionTitle:
                                              optionsControllers[1].text,
                                          optionID: UId.getId(),
                                          votesCount: 0,
                                        ),
                                        PollOptionsModel(
                                          optionTitle:
                                              optionsControllers[2].text,
                                          optionID: UId.getId(),
                                          votesCount: 0,
                                        ),
                                      ],
                                    )
                                    .then((value) {
                                      if (!context.mounted) return;
                                      Navigator.pop(context);
                                      _questionController.clear();

                                      optionsControllers[0].clear();
                                      optionsControllers[1].clear();
                                      optionsControllers[2].clear();
                                      context
                                          .read(
                                            HomeScreenCategoriesProviders
                                                .pollsScreenProvidersApis,
                                          )
                                          .rebuildPollsWidget();

                                      _fetchAllPolls = context
                                          .read(
                                            HomeScreenCategoriesProviders
                                                .pollsScreenProvidersApis,
                                          )
                                          .getAllPolls(context: context);
                                    });
                              }
                            },
                          );
                        }
                      },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

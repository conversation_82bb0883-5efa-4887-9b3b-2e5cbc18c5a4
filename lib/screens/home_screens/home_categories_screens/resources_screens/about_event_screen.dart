import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AboutEventScreen extends StatefulWidget {
  const AboutEventScreen({super.key});

  @override
  State<AboutEventScreen> createState() => _AboutEventScreenState();
}

class _AboutEventScreenState extends State<AboutEventScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(20.0.h),
          physics: const BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonComponents.comonTitleScreen(
                context: context,
                title: "About Event",
              ),
              SizedBox(height: 20.0.h),
              CommonComponents.imageAssetWithCache(
                context: context,
                image: AppImages.aboutEventTestImage,
                height: 213.0.h,
                width: 398.0.w,
                fit: BoxFit.contain,
              ),
              SizedBox(height: 15.0.h),
              Text(
                "37th AFA Int’l Technical Conference & Exhibition",
                style: TextStyle(
                  fontSize: 18.0.sp,
                  color: AppColors.blackColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 12.0.h),
              Text(
                "Welcome to the 37th AFA International Technical Conference & Exhibition! This prestigious event, hosted by the University Mohammed VI Polytechnic (UM6P), brings together leaders, innovators, and experts from the agri-nutrient industry to explore the latest advancements, share powerful research, and discuss strategies for sustainable growth in fertilizer production.",
                style: TextStyle(
                  fontSize: 14.0.sp,
                  color: AppColors.blackColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 10.0.h),
              Row(
                children: [
                  CommonComponents.imageAssetWithCache(
                    context: context,
                    image: AppImages.locationAboutEventIcon,
                    height: 21.0.h,
                    width: 16.0.w,
                    fit: BoxFit.contain,
                  ),
                  SizedBox(width: 10.0.w),
                  Expanded(
                    child: Text(
                      "Vibrant City of BenGuerir at the University of Mohammed VI Polytechnic University (UM6P), Morocco",
                      style: TextStyle(
                        fontSize: 12.0.sp,
                        color: AppColors.blackColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 10.0.h),
              Row(
                children: [
                  CommonComponents.imageAssetWithCache(
                    context: context,
                    image: AppImages.calendarAboutEventIcon,
                    height: 21.0.h,
                    width: 16.0.w,
                    fit: BoxFit.contain,
                  ),
                  SizedBox(width: 10.0.w),
                  Expanded(
                    child: Text(
                      "September 16-18, 2025 ",
                      style: TextStyle(
                        fontSize: 12.0.sp,
                        color: AppColors.blackColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:afa_app/app_config/api_providers/profile_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/speakers_screens_widgets/request_meeting_speaker_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class RequestMeetingScreenSpeaker extends StatefulWidget {
  const RequestMeetingScreenSpeaker({super.key});

  @override
  State<RequestMeetingScreenSpeaker> createState() =>
      _RequestMeetingScreenSpeakerState();
}

class _RequestMeetingScreenSpeakerState
    extends State<RequestMeetingScreenSpeaker> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _linkOrLocationController =
      TextEditingController();
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _timeController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _linkOrLocationController.dispose();
    _dateController.dispose();
    _timeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          padding: EdgeInsets.all(15.0.h),
          child: Form(
            key: _formKey,
            child: Column(
              spacing: 25.0.h,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Request Meeting",
                  style: TextStyle(
                    fontSize: 20.0.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.blackColor,
                  ),
                ),

                RequestMeetingSpeakerWidgets.requestMeettingFields(
                  context: context,
                  title: "Title",
                  controller: _titleController,
                  validate: "Please Enter Meeting Title",
                ),

                RequestMeetingSpeakerWidgets.requestMeettingFields(
                  context: context,
                  title: "Description",
                  controller: _descriptionController,
                  validate: "Please Enter Meeting Description",
                  maxlines: 5,
                ),

                RequestMeetingSpeakerWidgets.requestMeettingFields(
                  context: context,
                  title: "Link or Location",
                  controller: _linkOrLocationController,
                  validate: "Please Enter Meeting Link or Location",
                ),

                RequestMeetingSpeakerWidgets.requestMeettingFields(
                  context: context,
                  title: "Date",
                  controller: _dateController,
                  validate: "Please Enter Meeting Date",
                  showDateIcon: true,
                  readOnly: true,
                ),

                RequestMeetingSpeakerWidgets.requestMeettingFields(
                  context: context,
                  title: "Time",
                  controller: _timeController,
                  validate: "Please Enter Meeting Time",
                  showTimeIcon: true,
                  readOnly: true,
                ),

                ElevatedButton(
                  onPressed: () async {
                    if (_formKey.currentState!.validate()) {
                      await context
                          .read(ProfileProviders.commonApis)
                          .sendRequestMeeting(
                            context: context,
                            title: _titleController.text,
                            descriptipn: _descriptionController.text,
                            location: _linkOrLocationController.text,
                            date: _dateController.text,
                            time: _timeController.text,
                          );
                    }
                  },

                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: AppColors.midLevelGreenColor,
                    textStyle: TextStyle(
                      fontSize: 16.0.sp,
                      fontWeight: FontWeight.bold,
                    ),
                    minimumSize: Size(398.0.w, 46.0.h),
                  ),
                  child: const Text("Send"),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

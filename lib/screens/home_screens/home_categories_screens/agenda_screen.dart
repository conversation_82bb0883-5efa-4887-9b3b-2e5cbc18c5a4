import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_days_model.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_screen_model.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_sessions_model.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_speakers_model.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_time_slots_model.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/agenda_screens/agenda_details_screen.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/agenda_screen_widgets.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/agenda_screens_widgets/agenda_screen_details_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class AgendaScreen extends StatefulWidget {
  const AgendaScreen({super.key});

  @override
  State<AgendaScreen> createState() => _AgendaScreenState();
}

class _AgendaScreenState extends State<AgendaScreen> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _noteController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  Future<List<AgendaScreenModel>>? _fetchAgendaList;
  Future<List<dynamic>>? _fetchDaysList;
  Future<List<dynamic>>? _fetchSpeakersFilterList;
  Future<List<dynamic>>? _fetchTimeFilterList;
  Future<List<dynamic>>? _fetchSessionsFilterList;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context
          .read(HomeScreenCategoriesProviders.agendaScreenProvidersApis)
          .selectTimePeriod(null);

      context
          .read(HomeScreenCategoriesProviders.agendaScreenProvidersApis)
          .selectTimeSlots(null);

      context
          .read(HomeScreenCategoriesProviders.agendaScreenProvidersApis)
          .selectSpeaker(null);

      context
          .read(HomeScreenCategoriesProviders.agendaScreenProvidersApis)
          .selectSession(null);

      setState(() {
        _fetchAgendaList = context
            .read(HomeScreenCategoriesProviders.agendaScreenProvidersApis)
            .getAgendaList(context: context);

        _fetchDaysList = context
            .read(HomeScreenCategoriesProviders.agendaScreenProvidersApis)
            .getAgendaFiltersList(context: context, getTimePeriodsList: true);

        _fetchSpeakersFilterList = context
            .read(HomeScreenCategoriesProviders.agendaScreenProvidersApis)
            .getAgendaFiltersList(context: context, getSpeakersList: true);

        _fetchTimeFilterList = context
            .read(HomeScreenCategoriesProviders.agendaScreenProvidersApis)
            .getAgendaFiltersList(context: context, getTimeSlotsList: true);

        _fetchSessionsFilterList = context
            .read(HomeScreenCategoriesProviders.agendaScreenProvidersApis)
            .getAgendaFiltersList(context: context, getSessionList: true);
      });
    });

    super.initState();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_fetchAgendaList == null || _fetchDaysList == null) {
      return Center(child: CommonComponents.loadingDataFromServer());
    }
    return Scaffold(
      body: SafeArea(
        child: Consumer(
          builder: (context, watch, child) => FutureBuilder(
            key: watch
                .watch(HomeScreenCategoriesProviders.agendaScreenProvidersApis)
                .agendaKey,
            future: Future.wait([
              _fetchAgendaList!,
              _fetchDaysList!,
              _fetchSpeakersFilterList!,
              _fetchTimeFilterList!,
              _fetchSessionsFilterList!,
            ]),
            builder: (context, AsyncSnapshot<List<dynamic>> snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(child: CommonComponents.loadingDataFromServer());
              }
              if (snapshot.data == null) {
                return Center(child: CommonComponents.noDataFoundWidget());
              } else {
                final List<AgendaScreenModel> agendaDataList =
                    snapshot.data![0];
                final List<AgendaDaysModel> daysList = snapshot.data![1];
                final List<AgendaSpeakersModel> speakersList =
                    snapshot.data![2];

                final List<AgendaTimeSlotsModel> timesList = snapshot.data![3];
                final List<AgendaSessionsModel> sessionsList =
                    snapshot.data![4];

                return SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  padding: EdgeInsets.all(10.0.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CommonComponents.comonTitleScreen(
                        context: context,
                        title: "Agenda",
                      ),
                      SizedBox(height: 10.0.h),
                      AgendaScreenWidgets.agendaheaderWidget(
                        context: context,
                        controller: _searchController,
                        speakersList: speakersList,
                        timeSlotsList: timesList,
                        sessionList: sessionsList,
                        onSubmitted: (value) {
                          context
                              .read(
                                HomeScreenCategoriesProviders
                                    .agendaScreenProvidersApis,
                              )
                              .rebuildAgendaWidget();

                          if (value != " ") {
                            _fetchAgendaList = context
                                .read(
                                  HomeScreenCategoriesProviders
                                      .agendaScreenProvidersApis,
                                )
                                .getAgendaList(
                                  context: context,
                                  setInitialTimePeriod: false,
                                  isSerach: true,
                                  searchText: value,
                                );
                          } else {
                            _fetchAgendaList = context
                                .read(
                                  HomeScreenCategoriesProviders
                                      .agendaScreenProvidersApis,
                                )
                                .getAgendaList(
                                  context: context,
                                  setInitialTimePeriod: true,
                                );
                          }
                        },
                        onPress: () {
                          _searchController.clear();
                          context
                              .read(
                                HomeScreenCategoriesProviders
                                    .agendaScreenProvidersApis,
                              )
                              .rebuildAgendaWidget();

                          _fetchAgendaList = context
                              .read(
                                HomeScreenCategoriesProviders
                                    .agendaScreenProvidersApis,
                              )
                              .getAgendaList(
                                context: context,
                                setInitialTimePeriod: false,
                              );
                          if (!context.mounted) return;
                          Navigator.pop(context);
                        },
                      ),
                      SizedBox(height: 10.0.h),
                      SizedBox(
                        height: 80.0.h,
                        child: ListView.separated(
                          physics: const BouncingScrollPhysics(),
                          scrollDirection: Axis.horizontal,
                          separatorBuilder: (context, index) =>
                              SizedBox(width: 10.0.w),
                          itemCount: daysList.length,
                          itemBuilder: (context, index) => InkWell(
                            onTap: () {
                              _searchController.clear();
                              context
                                  .read(
                                    HomeScreenCategoriesProviders
                                        .agendaScreenProvidersApis,
                                  )
                                  .rebuildAgendaWidget();

                              context
                                  .read(
                                    HomeScreenCategoriesProviders
                                        .agendaScreenProvidersApis,
                                  )
                                  .selectTimePeriod(daysList[index]);

                              _fetchAgendaList = context
                                  .read(
                                    HomeScreenCategoriesProviders
                                        .agendaScreenProvidersApis,
                                  )
                                  .getAgendaList(
                                    context: context,
                                    setInitialTimePeriod: false,
                                  );
                            },
                            child: AgendaScreenWidgets.bubleDaysWidget(
                              context: context,
                              title: daysList[index].dayName!,
                              subTitle: "Day $index",
                              textColor:
                                  daysList[index].dayID ==
                                      watch
                                          .watch(
                                            HomeScreenCategoriesProviders
                                                .agendaScreenProvidersApis,
                                          )
                                          .getAgendaByTimePeriod
                                          ?.dayID
                                  ? AppColors.midLevelGreenColor
                                  : AppColors.blackColor,
                              containerColor:
                                  daysList[index].dayID ==
                                      watch
                                          .watch(
                                            HomeScreenCategoriesProviders
                                                .agendaScreenProvidersApis,
                                          )
                                          .getAgendaByTimePeriod
                                          ?.dayID
                                  ? AppColors.darkGreenColor
                                  : AppColors.lightgreyColor,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 10.0.h),

                      ListView.separated(
                        padding: EdgeInsets.all(10.0.h),
                        shrinkWrap: true,
                        physics: const BouncingScrollPhysics(),
                        separatorBuilder: (context, index) =>
                            SizedBox(height: 10.0.h),
                        itemCount: agendaDataList.length,
                        itemBuilder: (context, index) => Container(
                          padding: EdgeInsets.all(10.0.h),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.greyColor),
                            borderRadius: BorderRadius.all(
                              Radius.circular(16.0.r),
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${agendaDataList[index].sessionTitle}:\n(${agendaDataList[index].sessionStartTime} :${agendaDataList[index].sessionEndTime})",
                                style: TextStyle(
                                  fontSize: 16.0.sp,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.blackColor,
                                ),
                              ),
                              SizedBox(height: 10.0.h),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,

                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.alarm,
                                        size: 15.0.h,
                                        color: AppColors.midLevelGreenColor,
                                      ),
                                      Text(
                                        "${agendaDataList[index].sessionStartTime} – ${agendaDataList[index].sessionEndTime}",
                                        style: TextStyle(
                                          fontSize: 12.0.sp,
                                          color: AppColors.midLevelGreenColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                  InkWell(
                                    onTap: () async {
                                      await AgendaScreenDetailsWidgets.showNoteAlertWidget(
                                        context: context,
                                        controller: _noteController,
                                        formKey: _formKey,
                                      );
                                    },
                                    child: Container(
                                      alignment: Alignment.center,
                                      padding: EdgeInsets.all(10.0.h),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: AppColors.greyColor,
                                        ),
                                        borderRadius: BorderRadius.all(
                                          Radius.circular(24.0.r),
                                        ),
                                      ),
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.calendar_month,
                                            size: 16.0.h,
                                            color: AppColors.blackColor,
                                          ),
                                          SizedBox(width: 10.0.w),
                                          Text(
                                            "Take Note",
                                            style: TextStyle(
                                              fontSize: 12.0.sp,
                                              color: AppColors.blackColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                              SizedBox(height: 10.0.h),
                              Visibility(
                                visible: agendaDataList[index]
                                    .speakersList!
                                    .isNotEmpty,
                                child:
                                    AgendaScreenWidgets.showSpeakersListWidget(
                                      context: context,
                                      image: AppImages.speakerSmallIcon,

                                      speakerList:
                                          agendaDataList[index].speakersList!,
                                    ),
                              ),
                              Visibility(
                                visible: agendaDataList[index]
                                    .speakersList!
                                    .isNotEmpty,
                                child: Align(
                                  alignment: Alignment.centerRight,
                                  child: TextButton(
                                    onPressed: () {
                                      Navigator.pushNamed(
                                        context,
                                        PATHS.agendaDetailsScreen,
                                        arguments: AgendaDetailsScreen(
                                          agendaDetails: agendaDataList[index],
                                          agendaSpeakersDetails:
                                              agendaDataList[index]
                                                  .speakersList!,
                                        ),
                                      );
                                    },
                                    style: TextButton.styleFrom(
                                      foregroundColor:
                                          AppColors.midLevelGreenColor,
                                      padding: const EdgeInsets.all(0.0),
                                      tapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                      minimumSize: Size(27.0.w, 17.0.h),
                                      textStyle: TextStyle(
                                        fontSize: 12.0.sp,
                                        fontWeight: FontWeight.bold,
                                        decoration: TextDecoration.underline,
                                        height: 1.0.h,
                                      ),
                                    ),
                                    child: const Text("View Details"),
                                  ),
                                ),
                              ),
                              //   ],
                              // ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }
            },
          ),
        ),
      ),
    );
  }
}

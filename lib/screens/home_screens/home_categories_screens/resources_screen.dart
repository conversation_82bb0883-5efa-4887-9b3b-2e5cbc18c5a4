import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/resources_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ResourcesScreen extends StatefulWidget {
  const ResourcesScreen({super.key});

  @override
  State<ResourcesScreen> createState() => _ResourcesScreenState();
}

class _ResourcesScreenState extends State<ResourcesScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(10.0.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonComponents.comonTitleScreen(
                context: context,
                title: "Resources",
              ),
              <PERSON><PERSON><PERSON><PERSON>(height: 10.0.h),

              InkWell(
                onTap: () {
                  Navigator.pushNamed(context, PATHS.aboutEventScreen);
                },
                child: Container(
                  padding: EdgeInsets.all(10.0.h),
                  decoration: BoxDecoration(
                    color: AppColors.darkGreenColor,
                    borderRadius: BorderRadius.all(Radius.circular(16.0.r)),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          CommonComponents.imageAssetWithCache(
                            context: context,
                            image: AppImages.aboutEventIcon,
                            height: 32.0.h,
                            width: 32.0.w,
                            fit: BoxFit.contain,
                          ),
                          SizedBox(width: 10.0.w),
                          Text(
                            "About Event",
                            style: TextStyle(
                              fontSize: 16.0.sp,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                      Icon(
                        Icons.arrow_forward_ios_rounded,
                        color: Colors.white,
                        size: 17.0.h,
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 10.0.h),
              Expanded(
                child: GridView.builder(
                  itemCount: ResourcesScreenWidgets.resourcesFileds.length,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    mainAxisExtent: 90.0.h,
                    crossAxisSpacing: 10.0.w,
                    mainAxisSpacing: 10.0.h,
                  ),
                  itemBuilder: (context, index) => InkWell(
                    onTap: () {
                      if (ResourcesScreenWidgets
                                  .resourcesFileds[index]['title'] ==
                              "Logistics" ||
                          ResourcesScreenWidgets
                                  .resourcesFileds[index]['title'] ==
                              "Floormap") {
                        CommonComponents.launchOnBrowser(
                          context: context,
                          url:
                              "https://arabfertilizer.org/technical-conference/accommodation/",
                        );
                      } else {
                        Navigator.pushNamed(
                          context,
                          ResourcesScreenWidgets.resourcesFileds[index]['path'],
                        );
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.all(10.0.h),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.lightgreyColor),
                        borderRadius: BorderRadius.all(Radius.circular(16.0.r)),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CommonComponents.imageAssetWithCache(
                                context: context,
                                image: ResourcesScreenWidgets
                                    .resourcesFileds[index]['icon'],
                                height: 32.0.h,
                                width: 32.0.w,
                                fit: BoxFit.contain,
                              ),
                              SizedBox(height: 5.0.h),
                              Text(
                                ResourcesScreenWidgets
                                    .resourcesFileds[index]['title'],
                                style: TextStyle(
                                  fontSize: 16.0.sp,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.blackColor,
                                ),
                              ),
                            ],
                          ),
                          Icon(Icons.arrow_forward_ios, size: 17.0.h),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

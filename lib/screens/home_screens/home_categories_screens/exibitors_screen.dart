import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/home_screens_models/exibitors_screen_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class ExibitorsScreen extends StatefulWidget {
  const ExibitorsScreen({super.key});

  @override
  State<ExibitorsScreen> createState() => _ExibitorsScreenState();
}

class _ExibitorsScreenState extends State<ExibitorsScreen> {
  Future<List<ExibitorsScreenModel>>? _fetchExibitorsList;

  @override
  void initState() {
    _fetchExibitorsList = context
        .read(HomeScreenCategoriesProviders.exibitorsScreenProvidersApis)
        .getExibitorsList(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Safe<PERSON>rea(
        child: FutureBuilder(
          future: _fetchExibitorsList,
          builder:
              (context, AsyncSnapshot<List<ExibitorsScreenModel>> snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(
                    child: CommonComponents.loadingDataFromServer(),
                  );
                } else if (snapshot.data == null) {
                  return Center(child: CommonComponents.noDataFoundWidget());
                } else {
                  return Padding(
                    padding: EdgeInsets.all(15.0.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonComponents.comonTitleScreen(
                          context: context,
                          title: "Exhibitors",
                        ),
                        SizedBox(height: 10.0.h),
                        Expanded(
                          child: GridView.builder(
                            physics: const BouncingScrollPhysics(),
                            itemCount: snapshot.data!.length,
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 3,
                                  mainAxisExtent: 122.0.h,
                                  mainAxisSpacing: 20.0.h,
                                  crossAxisSpacing: 10.0.w,
                                ),
                            itemBuilder: (context, index) => InkWell(
                              onTap: () async {
                                // await CommonComponents.launchOnBrowser(
                                //   context: context,
                                //   url: snapshot.data![index].link!,
                                // );
                              },
                              child: Container(
                                padding: EdgeInsets.all(10.0.h),
                                height: 122.0.h,
                                width: 122.0.w,
                                decoration: BoxDecoration(
                                  // border: Border.all(
                                  //   color: AppColors.greyColor.withAlpha(
                                  //     (0.22 * 255).toInt(),
                                  //   ),
                                  // ),
                                  // color: AppColors.greyColor.withAlpha(
                                  //   (0.2 * 255).toInt(),
                                  // ),
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(8.0.r),
                                  ),
                                ),
                                child: CommonComponents.imageWithNetworkCache(
                                  context: context,
                                  image: snapshot.data![index].image!,
                                  height: 73.0.h,
                                  width: 86.0.w,
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }
              },
        ),
      ),
    );
  }
}

import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:afa_app/models/home_screens_models/attendance_screens_models/attendance_categories_model.dart';
import 'package:afa_app/models/home_screens_models/attendance_screens_models/attendance_screen_model.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/attendance_screens/attendance_details_screen.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/attendance_screens/request_meeting_screen_attendance.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/attendance_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:riverpod_context/riverpod_context.dart';

class AttendanceScreen extends StatefulWidget {
  const AttendanceScreen({super.key});

  @override
  State<AttendanceScreen> createState() => _AttendanceScreenState();
}

class _AttendanceScreenState extends State<AttendanceScreen> {
  final TextEditingController _addNoteController = TextEditingController();
  final TextEditingController _addFirstMessageController =
      TextEditingController();
  final _formKey = GlobalKey<FormState>();
  Future<List<AttendanceScreenModel>>? _fetchAttendanceList;
  Future<List<AttendanceCategoriesModel>>? _fetchAttendanceCategories;
  final RefreshController _refreshController = RefreshController(
    initialRefresh: false,
  );

  bool loadingAttendanceByCategory = false;

  @override
  void initState() {
    _fetchAttendanceList = context
        .read(HomeScreenCategoriesProviders.attendanceScreenProvidersApis)
        .getAttendanceList(
          context: context,
          controller: _refreshController,
          initialLoading: true,
        );

    _fetchAttendanceCategories = context
        .read(HomeScreenCategoriesProviders.attendanceScreenProvidersApis)
        .getAttendanceCategories(context: context);

    super.initState();
  }

  void _onLoadingAttendancePgination() async {
    await Future.delayed(Duration.zero, () async {
      if (!mounted) return;
      _fetchAttendanceList = context
          .read(HomeScreenCategoriesProviders.attendanceScreenProvidersApis)
          .getAttendanceList(
            context: context,
            controller: _refreshController,
            isLoading: true,
          );
    });
    setState(() {});
  }

  void _onRefreshAttendancePgination() async {
    await Future.delayed(Duration.zero, () async {
      if (!mounted) return;
      _fetchAttendanceList = context
          .read(HomeScreenCategoriesProviders.attendanceScreenProvidersApis)
          .getAttendanceList(
            context: context,
            controller: _refreshController,
            isRefresh: true,
          );
    });
    setState(() {});
  }

  @override
  void dispose() {
    _addNoteController.dispose();
    _refreshController.dispose();
    _addFirstMessageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Consumer(
          builder: (context, watch, child) => SmartRefresher(
            controller: _refreshController,
            enablePullUp: true,
            enablePullDown: true,
            onLoading: _onLoadingAttendancePgination,
            onRefresh: _onRefreshAttendancePgination,
            child: FutureBuilder(
              future: Future.wait([
                _fetchAttendanceList!,
                _fetchAttendanceCategories!,
              ]),
              builder: (context, AsyncSnapshot<List<dynamic>> snapshot) {
                if (snapshot.data == null) {
                  return Center(
                    child: CommonComponents.loadingDataFromServer(),
                  );
                } else {
                  final List<AttendanceScreenModel> attendanceData =
                      snapshot.data![0];
                  final List<AttendanceCategoriesModel> attendanceCategories =
                      snapshot.data![1];
                  return SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    padding: EdgeInsets.all(10.0.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonComponents.comonTitleScreen(
                          context: context,
                          title: "Attendees",
                        ),
                        SizedBox(height: 20.0.h),
                        AttendanceScreenWidgets.headerAttendanceWidget(
                          attendanceCategories: attendanceCategories,
                        ),
                        SizedBox(height: 60.0.h),
                        GridView.builder(
                          shrinkWrap: true,
                          itemCount: attendanceCategories.length,
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 4,
                                crossAxisSpacing: 0.0.h,
                                mainAxisSpacing: 0.0.h,
                                mainAxisExtent: 50.0.h,
                              ),
                          itemBuilder: (context, index) =>
                              AttendanceScreenWidgets.statisticsWidget(
                                title: attendanceCategories[index].name!,
                                subTitle: attendanceCategories[index].count!
                                    .toString(),
                                color: attendanceCategories[index].color!,
                              ),
                        ),

                        SizedBox(height: 10.0.h),
                        SizedBox(
                          height: 50.0.h,
                          child: ListView.separated(
                            physics: const BouncingScrollPhysics(),
                            scrollDirection: Axis.horizontal,
                            // shrinkWrap: true,
                            separatorBuilder: (context, index) =>
                                SizedBox(width: 10.0.w),
                            itemCount: attendanceCategories.length,
                            itemBuilder: (context, index) {
                              return InkWell(
                                onTap: () {
                                  context
                                      .read(
                                        HomeScreenCategoriesProviders
                                            .attendanceScreenProvidersApis,
                                      )
                                      .setCategorySelected(
                                        attendanceCategories[index],
                                      );

                                  setState(() {
                                    _fetchAttendanceList = context
                                        .read(
                                          HomeScreenCategoriesProviders
                                              .attendanceScreenProvidersApis,
                                        )
                                        .getAttendanceList(
                                          context: context,
                                          controller: _refreshController,
                                          initialLoading: true,
                                          showLoading: true,
                                        );
                                  });
                                },
                                child: Container(
                                  padding: EdgeInsets.all(15.0.h),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(100.0.r),
                                    ),
                                    color:
                                        watch
                                                .watch(
                                                  HomeScreenCategoriesProviders
                                                      .attendanceScreenProvidersApis,
                                                )
                                                .categorySelected ==
                                            attendanceCategories[index]
                                        ? AppColors.midLevelGreenColor
                                        : AppColors.lightGreenColor,
                                  ),
                                  child: Text(
                                    attendanceCategories[index].name!,
                                    style: TextStyle(
                                      fontSize: 14.0.sp,
                                      color:
                                          watch
                                                  .watch(
                                                    HomeScreenCategoriesProviders
                                                        .attendanceScreenProvidersApis,
                                                  )
                                                  .categorySelected ==
                                              attendanceCategories[index]
                                          ? Colors.white
                                          : AppColors.midLevelGreenColor,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        SizedBox(height: 10.0.h),

                        ListView.separated(
                          shrinkWrap: true,
                          physics: const BouncingScrollPhysics(),
                          separatorBuilder: (context, index) =>
                              SizedBox(height: 10.0.h),
                          itemCount: attendanceData.length,
                          itemBuilder: (context, index) => Container(
                            padding: EdgeInsets.all(10.0.h),
                            decoration: BoxDecoration(
                              border: Border.all(color: AppColors.greyColor),
                              borderRadius: BorderRadius.all(
                                Radius.circular(16.0.r),
                              ),
                            ),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,

                                      children: [
                                        ClipOval(
                                          child:
                                              CommonComponents.imageWithNetworkCache(
                                                context: context,
                                                image: attendanceData[index]
                                                    .userImage!,
                                                height: 40.0.h,
                                                width: 40.0.w,
                                                fit: BoxFit.contain,
                                              ),
                                        ),
                                        SizedBox(width: 5.0.w),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              attendanceData[index].userName!,
                                              style: TextStyle(
                                                fontSize: 14.0.sp,
                                                color: AppColors.blackColor,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            // SizedBox(height: 10.0.h),
                                            Container(
                                              padding: EdgeInsets.symmetric(
                                                horizontal: 15.0.w,
                                                vertical: 7.0.h,
                                              ),
                                              alignment: Alignment.center,

                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.all(
                                                  Radius.circular(16.0.r),
                                                ),
                                                color:
                                                    AppColors.lightGreenColor,
                                              ),
                                              child: Text(
                                                attendanceData[index]
                                                    .categorySelected!,
                                                style: TextStyle(
                                                  fontSize: 14.0.sp,
                                                  color: AppColors
                                                      .midLevelGreenColor,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    Container(
                                      alignment: Alignment.center,
                                      padding: EdgeInsets.all(10.0.h),
                                      decoration: const BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: AppColors.lightgreyColor,
                                      ),
                                      child: Icon(
                                        Icons.bookmark_border,
                                        size: 15.0.h,
                                        color: AppColors.midLevelGreenColor,
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 10.0.h),
                                AttendanceScreenWidgets.sessionWidgetFileds(
                                  context: context,
                                  image: AppImages.jobIcon,
                                  title: "Job :",
                                  subTitle: attendanceData[index].jobName!,
                                ),
                                SizedBox(height: 10.0.h),
                                AttendanceScreenWidgets.sessionWidgetFileds(
                                  context: context,
                                  image: AppImages.companyIcon,
                                  title: "Company:",
                                  subTitle: attendanceData[index].company!,
                                ),
                                SizedBox(height: 10.0.h),
                                AttendanceScreenWidgets.sessionWidgetFileds(
                                  context: context,
                                  image: AppImages.countryIcon,
                                  title: "Country :",
                                  subTitle: attendanceData[index].country!,
                                ),
                                SizedBox(height: 20.0.h),
                                const Divider(color: AppColors.greyColor),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        AttendanceScreenWidgets.sessionButtonsWidget(
                                          context: context,
                                          image: AppImages.noteIcon,
                                          onPress: () async {
                                            await AttendanceScreenWidgets.showNoteAlertWidget(
                                              context: context,
                                              controller: _addNoteController,
                                              formKey: _formKey,
                                            );
                                          },
                                        ),
                                        SizedBox(width: 10.0.w),
                                        AttendanceScreenWidgets.sessionButtonsWidget(
                                          context: context,
                                          image: AppImages.messageIcon,
                                          onPress: () async {
                                            await AttendanceScreenWidgets.showSendNewMessageAlertWidget(
                                              context: context,
                                              controller:
                                                  _addFirstMessageController,
                                              formKey: _formKey,
                                              attendanceID: int.parse(
                                                attendanceData[index]
                                                    .attendanceID!,
                                              ),
                                            );
                                          },
                                        ),
                                        SizedBox(width: 10.0.w),
                                        AttendanceScreenWidgets.sessionButtonsWidget(
                                          context: context,
                                          image: AppImages.videoIcon,
                                          onPress: () {
                                            Navigator.pushNamed(
                                              context,
                                              PATHS
                                                  .requestMeetingScreenAttendance,
                                              arguments:
                                                  RequestMeetingScreenAttendance(
                                                    attendanceDetails:
                                                        attendanceData[index],
                                                  ),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                    TextButton(
                                      onPressed: () {
                                        Navigator.pushNamed(
                                          context,
                                          PATHS.attendanceDetailsScreen,
                                          arguments: AttendanceDetailsScreen(
                                            attendanceDetails:
                                                attendanceData[index],
                                          ),
                                        );
                                      },
                                      style: TextButton.styleFrom(
                                        foregroundColor:
                                            AppColors.midLevelGreenColor,
                                        padding: const EdgeInsets.all(0.0),
                                        tapTargetSize:
                                            MaterialTapTargetSize.shrinkWrap,
                                        minimumSize: Size(27.0.w, 17.0.h),
                                        textStyle: TextStyle(
                                          fontSize: 12.0.sp,
                                          fontWeight: FontWeight.bold,
                                          decoration: TextDecoration.underline,
                                          height: 1.0.h,
                                        ),
                                      ),
                                      child: const Text("View"),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }
              },
            ),
          ),
        ),
      ),
    );
  }
}

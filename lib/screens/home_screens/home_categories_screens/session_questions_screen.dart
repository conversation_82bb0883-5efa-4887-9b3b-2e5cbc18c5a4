import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:afa_app/models/home_screens_models/sessions_q&a_models/session_questions_model.dart';
import 'package:afa_app/models/home_screens_models/sessions_q&a_models/session_time_periods_model.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/questions_and_answers_screens_widgets/session_questions_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class SessionQuestionsScreen extends StatefulWidget {
  const SessionQuestionsScreen({super.key});

  @override
  State<SessionQuestionsScreen> createState() => _SessionQuestionsScreenState();
}

class _SessionQuestionsScreenState extends State<SessionQuestionsScreen> {
  final TextEditingController _addQuestionAndAnswerController =
      TextEditingController();

  final _formKey = GlobalKey<FormState>();

  Future<List<SessionQAndADaysModel>>? _fetchDaysList;
  Future<List<SessionQuestionsModel>>? _fetchSessionQuestions;

  @override
  void initState() {
    _fetchDaysList = context
        .read(HomeScreenCategoriesProviders.questionAndAnswersProvidersApis)
        .getQAndADaysList(context: context);

    _fetchSessionQuestions = context
        .read(HomeScreenCategoriesProviders.questionAndAnswersProvidersApis)
        .getSessionQuestions(context: context, setInitialTimePeriod: true);

    setState(() {});

    super.initState();
  }

  @override
  void dispose() {
    _addQuestionAndAnswerController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Consumer(
          builder: (context, watch, child) => FutureBuilder(
            future: Future.wait([_fetchDaysList!, _fetchSessionQuestions!]),
            builder: (context, AsyncSnapshot<List<dynamic>> snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(child: CommonComponents.loadingDataFromServer());
              } else if (snapshot.data == null) {
                return Center(child: CommonComponents.noDataFoundWidget());
              } else {
                final List<SessionQAndADaysModel> daysList = snapshot.data![0];
                final List<SessionQuestionsModel> sessionQuestionsList =
                    snapshot.data![1];
                return SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  padding: EdgeInsets.all(10.0.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CommonComponents.comonTitleScreen(
                        context: context,
                        title: "Session Q&A",
                      ),

                      SizedBox(height: 10.0.h),
                      SizedBox(
                        height: 80.0.h,
                        child: ListView.separated(
                          physics: const BouncingScrollPhysics(),
                          scrollDirection: Axis.horizontal,
                          separatorBuilder: (context, index) =>
                              SizedBox(width: 10.0.w),
                          itemCount: daysList.length,
                          itemBuilder: (context, index) => InkWell(
                            onTap: () {
                              context
                                  .read(
                                    HomeScreenCategoriesProviders
                                        .questionAndAnswersProvidersApis,
                                  )
                                  .rebuildQandAWidget();

                              context
                                  .read(
                                    HomeScreenCategoriesProviders
                                        .questionAndAnswersProvidersApis,
                                  )
                                  .selectTimePeriod(daysList[index]);

                              _fetchSessionQuestions = context
                                  .read(
                                    HomeScreenCategoriesProviders
                                        .questionAndAnswersProvidersApis,
                                  )
                                  .getSessionQuestions(
                                    context: context,
                                    setInitialTimePeriod: false,
                                  );
                            },
                            child: SessionQuestionsScreenWidgets.bubleDaysWidget(
                              context: context,
                              title: daysList[index].dayName!,
                              subTitle: "Day ${index + 1}",

                              textColor:
                                  daysList[index].dayID ==
                                      watch
                                          .watch(
                                            HomeScreenCategoriesProviders
                                                .questionAndAnswersProvidersApis,
                                          )
                                          .getQandAByTimePeriod
                                          ?.dayID
                                  ? AppColors.midLevelGreenColor
                                  : AppColors.blackColor,
                              containerColor:
                                  daysList[index].dayID ==
                                      watch
                                          .watch(
                                            HomeScreenCategoriesProviders
                                                .questionAndAnswersProvidersApis,
                                          )
                                          .getQandAByTimePeriod
                                          ?.dayID
                                  ? AppColors.darkGreenColor
                                  : AppColors.lightgreyColor,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 10.0.h),

                      ListView.separated(
                        padding: EdgeInsets.all(10.0.h),
                        shrinkWrap: true,
                        physics: const BouncingScrollPhysics(),
                        separatorBuilder: (context, index) =>
                            SizedBox(height: 10.0.h),
                        itemCount: sessionQuestionsList.length,
                        itemBuilder: (context, index) => Container(
                          padding: EdgeInsets.all(10.0.h),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.greyColor),
                            borderRadius: BorderRadius.all(
                              Radius.circular(16.0.r),
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${sessionQuestionsList[index].title}:\n${sessionQuestionsList[index].startTime} :${sessionQuestionsList[index].endTime})",
                                style: TextStyle(
                                  fontSize: 16.0.sp,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.blackColor,
                                ),
                              ),

                              SizedBox(height: 10.0.h),

                              Visibility(
                                visible: sessionQuestionsList[index]
                                    .speakers!
                                    .isNotEmpty,
                                child:
                                    SessionQuestionsScreenWidgets.showSpeakersListWidget(
                                      context: context,
                                      image: AppImages.speakerSmallIcon,

                                      speakerList:
                                          sessionQuestionsList[index].speakers!,
                                    ),
                              ),

                              Row(
                                children: [
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed: () async {
                                        SessionQuestionsScreenWidgets.showAddQuestionAlertWidget(
                                          context: context,
                                          controller:
                                              _addQuestionAndAnswerController,
                                          formKey: _formKey,
                                          sessionID: sessionQuestionsList[index]
                                              .sesionID!,
                                        );
                                      },

                                      style: ElevatedButton.styleFrom(
                                        foregroundColor: AppColors.blackColor,
                                        backgroundColor: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.all(
                                            Radius.circular(40.0.r),
                                          ),
                                          side: const BorderSide(
                                            color: AppColors.greyColor,
                                          ),
                                        ),
                                        textStyle: TextStyle(
                                          fontSize: 14.0.sp,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        minimumSize: Size(132.0.w, 40.0.h),
                                      ),
                                      child: const Text("Ask Question"),
                                    ),
                                  ),
                                  SizedBox(width: 10.0.w),
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed: () async {
                                        Navigator.pushNamed(
                                          context,
                                          PATHS.questionScreen,
                                        );
                                      },

                                      style: ElevatedButton.styleFrom(
                                        foregroundColor:
                                            AppColors.midLevelGreenColor,
                                        backgroundColor: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.all(
                                            Radius.circular(40.0.r),
                                          ),
                                          side: const BorderSide(
                                            color: AppColors.greyColor,
                                          ),
                                        ),
                                        minimumSize: Size(118.0.w, 40.0.h),
                                        textStyle: TextStyle(
                                          fontSize: 14.0.sp,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      child: const Text("View Questions"),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }
            },
          ),
        ),
      ),
    );
  }
}

import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/home_screens_models/sponsors_screen_models/sponsor_screen_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class SponsorsScreen extends StatefulWidget {
  const SponsorsScreen({super.key});

  @override
  State<SponsorsScreen> createState() => _SponsorsScreenState();
}

class _SponsorsScreenState extends State<SponsorsScreen> {
  Future<List<SponsorScreenModel>>? _fetchSponsorsList;

  @override
  void initState() {
    _fetchSponsorsList = context
        .read(HomeScreenCategoriesProviders.sponsorsScreenProvidersApis)
        .getSponsorsList(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: FutureBuilder(
          future: _fetchSponsorsList,
          builder: (context, AsyncSnapshot<List<SponsorScreenModel>> snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(child: CommonComponents.loadingDataFromServer());
            } else if (snapshot.data == null) {
              return Center(child: CommonComponents.noDataFoundWidget());
            } else {
              return Padding(
                padding: EdgeInsets.all(10.0.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonComponents.comonTitleScreen(
                      context: context,
                      title: "Sponsors",
                    ),
                    SizedBox(height: 10.0.h),
                    Expanded(
                      child: ListView.separated(
                        separatorBuilder: (context, categoryIndex) =>
                            SizedBox(height: 10.0.h),
                        itemCount: snapshot.data!.length,
                        itemBuilder: (context, categoryIndex) => Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Center(
                              child: SizedBox(
                                width: double.infinity,
                                child: Card(
                                  color: AppColors.darkGreenColor,
                                  child: Padding(
                                    padding: EdgeInsets.all(20.0.h),
                                    child: Text(
                                      snapshot
                                          .data![categoryIndex]
                                          .categoryName!,
                                      style: TextStyle(
                                        fontSize: 15.0.sp,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(height: 10.0.h),
                            GridView.builder(
                              shrinkWrap: true,
                              physics: const BouncingScrollPhysics(),
                              itemCount:
                                  snapshot.data![categoryIndex].images!.length,
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 3,
                                    mainAxisExtent: 130.0.h,
                                    mainAxisSpacing: 20.0.h,
                                    crossAxisSpacing: 10.0.w,
                                  ),
                              itemBuilder: (context, imageIndex) => InkWell(
                                onTap: () async {
                                  // await CommonComponents.launchOnBrowser(
                                  //   context: context,
                                  //   url: snapshot.data![index].link!,
                                  // );
                                },
                                child: Container(
                                  padding: EdgeInsets.all(10.0.h),

                                  decoration: BoxDecoration(
                                    // border: Border.all(
                                    //   color: AppColors.greyColor.withAlpha(
                                    //     (0.22 * 255).toInt(),
                                    //   ),
                                    // ),
                                    // color: AppColors.greyColor.withAlpha(
                                    //   (0.2 * 255).toInt(),
                                    // ),
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(8.0.r),
                                    ),
                                  ),

                                  child: CommonComponents.imageWithNetworkCache(
                                    context: context,
                                    image: snapshot
                                        .data![categoryIndex]
                                        .images![imageIndex]!,
                                    height: 50.0.h,
                                    width: 100.0.w,
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }
          },
        ),
      ),
    );
  }
}

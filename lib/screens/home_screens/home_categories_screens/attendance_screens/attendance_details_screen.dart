import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:afa_app/models/home_screens_models/attendance_screens_models/attendance_screen_model.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/attendance_screens/request_meeting_screen_attendance.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/attendance_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AttendanceDetailsScreen extends StatefulWidget {
  const AttendanceDetailsScreen({super.key, this.attendanceDetails});
  final AttendanceScreenModel? attendanceDetails;

  @override
  State<AttendanceDetailsScreen> createState() =>
      _AttendanceDetailsScreenState();
}

class _AttendanceDetailsScreenState extends State<AttendanceDetailsScreen> {
  final TextEditingController _addNoteController = TextEditingController();
  final TextEditingController _addFirstMessageController =
      TextEditingController();
  final _formKey = GlobalKey<FormState>();
  @override
  void dispose() {
    _addNoteController.dispose();
    _addFirstMessageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final AttendanceDetailsScreen args =
        ModalRoute.of(context)!.settings.arguments as AttendanceDetailsScreen;
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(10.0.h),
          physics: const BouncingScrollPhysics(),
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(10.0.h),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.greyColor),
                  borderRadius: BorderRadius.all(Radius.circular(16.0.r)),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,

                          children: [
                            ClipOval(
                              child: CommonComponents.imageWithNetworkCache(
                                context: context,
                                image: args.attendanceDetails!.userImage!,
                                height: 40.0.h,
                                width: 40.0.w,
                                fit: BoxFit.contain,
                              ),
                            ),
                            SizedBox(width: 5.0.w),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  args.attendanceDetails!.userName!,
                                  style: TextStyle(
                                    fontSize: 14.0.sp,
                                    color: AppColors.blackColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                // SizedBox(height: 10.0.h),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 15.0.w,
                                    vertical: 7.0.h,
                                  ),
                                  alignment: Alignment.center,

                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(16.0.r),
                                    ),
                                    color: AppColors.lightGreenColor,
                                  ),
                                  child: Text(
                                    args.attendanceDetails!.categorySelected!,
                                    style: TextStyle(
                                      fontSize: 14.0.sp,
                                      color: AppColors.midLevelGreenColor,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        Container(
                          alignment: Alignment.center,
                          padding: EdgeInsets.all(10.0.h),
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.lightgreyColor,
                          ),
                          child: Icon(
                            Icons.bookmark_border,
                            size: 15.0.h,
                            color: AppColors.midLevelGreenColor,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10.0.h),
                    AttendanceScreenWidgets.sessionWidgetFileds(
                      context: context,
                      image: AppImages.jobIcon,
                      title: "Job :",
                      subTitle: args.attendanceDetails!.jobName!,
                    ),
                    SizedBox(height: 10.0.h),
                    AttendanceScreenWidgets.sessionWidgetFileds(
                      context: context,
                      image: AppImages.companyIcon,
                      title: "Company:",
                      subTitle: args.attendanceDetails!.company!,
                    ),
                    SizedBox(height: 10.0.h),
                    AttendanceScreenWidgets.sessionWidgetFileds(
                      context: context,
                      image: AppImages.countryIcon,
                      title: "Country :",
                      subTitle: args.attendanceDetails!.country!,
                    ),
                    SizedBox(height: 20.0.h),
                    const Divider(color: AppColors.greyColor),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            AttendanceScreenWidgets.sessionButtonsWidget(
                              context: context,
                              image: AppImages.noteIcon,
                              onPress: () async {
                                await AttendanceScreenWidgets.showNoteAlertWidget(
                                  context: context,
                                  controller: _addNoteController,
                                  formKey: _formKey,
                                );
                              },
                            ),
                            SizedBox(width: 10.0.w),
                            AttendanceScreenWidgets.sessionButtonsWidget(
                              context: context,
                              image: AppImages.messageIcon,
                              onPress: () async {
                                await AttendanceScreenWidgets.showSendNewMessageAlertWidget(
                                  context: context,
                                  controller: _addFirstMessageController,
                                  formKey: _formKey,
                                  attendanceID: int.parse(
                                    args.attendanceDetails!.attendanceID!,
                                  ),
                                );
                              },
                            ),
                            SizedBox(width: 10.0.w),
                            AttendanceScreenWidgets.sessionButtonsWidget(
                              context: context,
                              image: AppImages.videoIcon,
                              onPress: () {
                                Navigator.pushNamed(
                                  context,
                                  PATHS.requestMeetingScreenAttendance,
                                  arguments: RequestMeetingScreenAttendance(
                                    attendanceDetails: args.attendanceDetails,
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                        // TextButton(
                        //   onPressed: () {},
                        //   style: TextButton.styleFrom(
                        //     foregroundColor: AppColors.midLevelGreenColor,
                        //     padding: const EdgeInsets.all(0.0),
                        //     tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        //     minimumSize: Size(27.0.w, 17.0.h),
                        //     textStyle: TextStyle(
                        //       fontSize: 12.0.sp,
                        //       fontWeight: FontWeight.bold,
                        //       decoration: TextDecoration.underline,
                        //       height: 1.0.h,
                        //     ),
                        //   ),
                        //   child: const Text("View"),
                        // ),
                      ],
                    ),
                  ],
                ),
              ),
              SizedBox(height: 10.0.h),
              Container(
                padding: EdgeInsets.all(10.0.h),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.greyColor),
                  borderRadius: BorderRadius.all(Radius.circular(24.0.r)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        CommonComponents.imageAssetWithCache(
                          context: context,
                          image: AppImages.aboutIcon,
                          height: 16.0.h,
                          width: 16.0.w,
                          fit: BoxFit.contain,
                        ),
                        SizedBox(width: 5.0.w),
                        Text(
                          "About :",
                          style: TextStyle(
                            fontSize: 12.0.sp,
                            color: AppColors.greyColor,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10.0.h),
                    Text(
                      args.attendanceDetails!.biography!,
                      style: TextStyle(
                        fontSize: 10.0.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textColor,
                      ),
                    ),
                    SizedBox(height: 10.0.h),
                    const Divider(color: AppColors.greyColor),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        CommonComponents.imageAssetWithCache(
                          context: context,
                          image: AppImages.articeIcon,
                          height: 16.0.h,
                          width: 16.0.w,
                          fit: BoxFit.contain,
                        ),
                        SizedBox(width: 5.0.w),
                        Text(
                          "Speaking at :",
                          style: TextStyle(
                            fontSize: 12.0.sp,
                            color: AppColors.greyColor,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10.0.h),
                    Text(
                      args.attendanceDetails!.speakingAt!,
                      style: TextStyle(
                        fontSize: 10.0.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textColor,
                      ),
                    ),
                    SizedBox(height: 10.0.h),
                    const Divider(color: AppColors.greyColor),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        CommonComponents.imageAssetWithCache(
                          context: context,
                          image: AppImages.expierenceIcon,
                          height: 16.0.h,
                          width: 16.0.w,
                          fit: BoxFit.contain,
                        ),
                        SizedBox(width: 5.0.w),
                        Text(
                          "Experience :",
                          style: TextStyle(
                            fontSize: 12.0.sp,
                            color: AppColors.greyColor,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10.0.h),

                    ListView.separated(
                      shrinkWrap: true,
                      physics: const BouncingScrollPhysics(),
                      separatorBuilder: (context, index) =>
                          SizedBox(height: 10.0.h),
                      itemCount: args.attendanceDetails!.experiences!.length,
                      itemBuilder: (context, index) => Row(
                        children: [
                          CommonComponents.imageWithNetworkCache(
                            image:
                                args
                                    .attendanceDetails!
                                    .experiences![index]
                                    .companyImage ??
                                CommonComponents.imageNotFound,
                            context: context,
                            height: 24.0.h,
                            width: 24.0.w,
                            fit: BoxFit.contain,
                          ),

                          SizedBox(width: 5.0.w),
                          Text(
                            args
                                .attendanceDetails!
                                .experiences![index]
                                .companyName!,
                            style: TextStyle(
                              fontSize: 10.0.sp,
                              color: AppColors.blackColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Divider(color: AppColors.greyColor),

                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        CommonComponents.imageAssetWithCache(
                          context: context,
                          image: AppImages.linksIcon,
                          height: 16.0.h,
                          width: 16.0.w,
                          fit: BoxFit.contain,
                        ),
                        SizedBox(width: 5.0.w),
                        Text(
                          "Links :",
                          style: TextStyle(
                            fontSize: 12.0.sp,
                            color: AppColors.greyColor,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10.0.h),
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const BouncingScrollPhysics(),
                      separatorBuilder: (context, index) =>
                          SizedBox(height: 10.0.h),
                      itemCount: args.attendanceDetails!.socialLinks!.length,
                      itemBuilder: (context, index) => Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            args.attendanceDetails!.socialLinks![index].title!,
                            style: TextStyle(fontSize: 10.0.sp),
                          ),
                          InkWell(
                            onTap: () async {
                              await CommonComponents.launchOnBrowser(
                                context: context,
                                url: args
                                    .attendanceDetails!
                                    .socialLinks![index]
                                    .url!,
                              );
                            },
                            child: CommonComponents.imageAssetWithCache(
                              context: context,
                              image: AppImages.shareIcon,
                              height: 20.0.h,
                              width: 20.0.w,
                              fit: BoxFit.contain,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

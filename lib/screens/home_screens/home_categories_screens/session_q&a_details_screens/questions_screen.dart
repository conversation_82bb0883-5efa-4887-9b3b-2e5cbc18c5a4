import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/home_screens_models/sessions_q&a_models/question_model.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/questions_and_answers_screens_widgets/questions_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class QuestionsScreen extends StatefulWidget {
  const QuestionsScreen({super.key});

  @override
  State<QuestionsScreen> createState() => _QuestionsScreenState();
}

class _QuestionsScreenState extends State<QuestionsScreen> {
  Future<List<QuestionsModel>>? _fetchSessionQuestions;
  @override
  void initState() {
    _fetchSessionQuestions = context
        .read(HomeScreenCategoriesProviders.questionAndAnswersProvidersApis)
        .getQuestions(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            CommonComponents.comonTitleScreen(
              context: context,
              title: "Questions",
            ),

            SizedBox(height: 20.0.h),
            Expanded(
              child: FutureBuilder(
                future: _fetchSessionQuestions,
                builder: (context, AsyncSnapshot<List<QuestionsModel>> snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return Center(
                      child: CommonComponents.loadingDataFromServer(),
                    );
                  } else if (snapshot.data == null) {
                    return Center(child: CommonComponents.noDataFoundWidget());
                  } else {
                    return ListView.separated(
                      padding: EdgeInsets.all(10.0.h),
                      separatorBuilder: (context, index) =>
                          SizedBox(height: 20.0.h),
                      itemCount: snapshot.data!.length,
                      itemBuilder: (context, index) => Container(
                        padding: EdgeInsets.all(10.0.h),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.greyColor),
                          borderRadius: BorderRadius.all(
                            Radius.circular(16.0.r),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                ClipOval(
                                  child: snapshot.data![index].userImage != null
                                      ? CommonComponents.imageWithNetworkCache(
                                          context: context,
                                          image:
                                              snapshot.data![index].userImage!,
                                          height: 40.0.h,
                                          width: 40.0.w,
                                          fit: BoxFit.contain,
                                        )
                                      : CommonComponents.imageAssetWithCache(
                                          context: context,
                                          image: AppImages.userPhoto,
                                          height: 40.0.h,
                                          width: 40.0.w,
                                          fit: BoxFit.contain,
                                        ),
                                ),
                                SizedBox(width: 10.0.w),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      snapshot.data![index].userName!,
                                      style: TextStyle(
                                        fontSize: 14.0.sp,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.blackColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            const Divider(color: AppColors.greyColor),
                            Text(
                              snapshot.data![index].question!,
                              style: TextStyle(
                                fontSize: 12.0.sp,
                                fontWeight: FontWeight.bold,
                                color: AppColors.blackColor,
                              ),
                            ),
                            SizedBox(height: 10.0.h),
                            Visibility(
                              visible:
                                  snapshot.data![index].answers!.isNotEmpty,
                              child: ElevatedButton(
                                onPressed: () async {
                                  QuestionsScreenWidgets.showAnswerAlertWidget(
                                    context: context,
                                    answersList: snapshot.data![index].answers!,
                                  );
                                },
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: Colors.white,
                                  backgroundColor: AppColors.midLevelGreenColor,
                                  textStyle: TextStyle(
                                    fontSize: 16.0.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  minimumSize: Size(398.0.w, 46.0.h),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(32.0.r),
                                    ),
                                  ),
                                ),
                                child: const Text("View Answers"),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

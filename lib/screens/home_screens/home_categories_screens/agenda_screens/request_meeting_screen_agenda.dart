// import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
// import 'package:afa_app/app_config/app_colors.dart';
// import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_screen_model.dart';
// import 'package:afa_app/widgets/home_screens_widgets.dart/agenda_screens_widgets/rquest_meeting_agenda_widgets.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:intl/intl.dart';
// import 'package:riverpod_context/riverpod_context.dart';

// class RequestMeetingScreenAgenda extends StatefulWidget {
//   const RequestMeetingScreenAgenda({super.key, this.agendaDetails});
//   final AgendaScreenModel? agendaDetails;

//   @override
//   State<RequestMeetingScreenAgenda> createState() =>
//       _RequestMeetingScreenAgendaState();
// }

// class _RequestMeetingScreenAgendaState
//     extends State<RequestMeetingScreenAgenda> {
//   final TextEditingController _titleController = TextEditingController();
//   final TextEditingController _descriptionController = TextEditingController();
//   final TextEditingController _linkOrLocationController =
//       TextEditingController();
//   final TextEditingController _dateController = TextEditingController();
//   final TextEditingController _timeController = TextEditingController();
//   final _formKey = GlobalKey<FormState>();
//   @override
//   void dispose() {
//     _titleController.dispose();
//     _descriptionController.dispose();
//     _linkOrLocationController.dispose();
//     _dateController.dispose();
//     _timeController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final RequestMeetingScreenAgenda args =
//         ModalRoute.of(context)!.settings.arguments
//             as RequestMeetingScreenAgenda;
//     return Scaffold(
//       body: SafeArea(
//         child: SingleChildScrollView(
//           physics: const BouncingScrollPhysics(),
//           padding: EdgeInsets.all(15.0.h),
//           child: Form(
//             key: _formKey,
//             child: Column(
//               spacing: 25.0.h,
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   "Request Meeting",
//                   style: TextStyle(
//                     fontSize: 20.0.sp,
//                     fontWeight: FontWeight.bold,
//                     color: AppColors.blackColor,
//                   ),
//                 ),

//                 RquestMeetingAgendaWidgets.requestMeettingFields(
//                   context: context,
//                   title: "Title",
//                   controller: _titleController,
//                   validate: "Please Enter Meeting Title",
//                 ),

//                 RquestMeetingAgendaWidgets.requestMeettingFields(
//                   context: context,
//                   title: "Description",
//                   controller: _descriptionController,
//                   validate: "Please Enter Meeting Description",
//                   maxlines: 5,
//                 ),

//                 RquestMeetingAgendaWidgets.requestMeettingFields(
//                   context: context,
//                   title: "Link or Location",
//                   controller: _linkOrLocationController,
//                   validate: "Please Enter Meeting Link or Location",
//                 ),

//                 RquestMeetingAgendaWidgets.requestMeettingFields(
//                   context: context,
//                   title: "Date",
//                   controller: _dateController,
//                   validate: "Please Enter Meeting Date",
//                   showDateIcon: true,
//                   readOnly: true,
//                   onPress: () async {
//                     await showDatePicker(
//                       context: context,
//                       firstDate: DateTime.now(),
//                       lastDate: DateTime(2050),
//                     ).then((value) {
//                       if (value != null) {
//                         _dateController.text = DateFormat(
//                           'yyyy-MM-dd',
//                         ).format(value);
//                       }
//                     });
//                   },
//                 ),

//                 RquestMeetingAgendaWidgets.requestMeettingFields(
//                   context: context,
//                   title: "Time",
//                   controller: _timeController,
//                   validate: "Please Enter Meeting Time",
//                   showTimeIcon: true,
//                   readOnly: true,
//                   onPress: () async {
//                     await showTimePicker(
//                       context: context,
//                       initialTime: TimeOfDay.now(),
//                     ).then((value) {
//                       if (!context.mounted) return;
//                       _timeController.text = value!.format(context);
//                     });
//                   },
//                 ),

//                 ElevatedButton(
//                   onPressed: () async {
//                     if (_formKey.currentState!.validate()) {
//                       await context
//                           .read(
//                             HomeScreenCategoriesProviders
//                                 .agendaScreenProvidersApis,
//                           )
//                           .sendRequestMeeting(
//                             context: context,
//                             agendaID: args.agendaDetails!.agendaID!,
//                             title: _titleController.text,
//                             description: _descriptionController.text,
//                             location: _linkOrLocationController.text,
//                             date: _dateController.text,
//                             time: _timeController.text,
//                           );
//                     }
//                   },

//                   style: ElevatedButton.styleFrom(
//                     foregroundColor: Colors.white,
//                     backgroundColor: AppColors.midLevelGreenColor,
//                     textStyle: TextStyle(
//                       fontSize: 16.0.sp,
//                       fontWeight: FontWeight.bold,
//                     ),
//                     minimumSize: Size(398.0.w, 46.0.h),
//                   ),
//                   child: const Text("Send"),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

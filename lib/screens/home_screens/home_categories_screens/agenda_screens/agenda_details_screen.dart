import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_details_speakers_model.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_screen_model.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/agenda_screens/agenda_details_speaker_screen.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/agenda_screens_widgets/agenda_screen_details_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AgendaDetailsScreen extends StatefulWidget {
  const AgendaDetailsScreen({
    super.key,
    this.agendaSpeakersDetails,
    this.agendaDetails,
  });
  final List<AgendaDetailsSpeakersModel>? agendaSpeakersDetails;
  final AgendaScreenModel? agendaDetails;

  @override
  State<AgendaDetailsScreen> createState() => _AgendaDetailsScreenState();
}

class _AgendaDetailsScreenState extends State<AgendaDetailsScreen> {
  final TextEditingController _noteController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final AgendaDetailsScreen args =
        ModalRoute.of(context)!.settings.arguments as AgendaDetailsScreen;
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(10.0.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CommonComponents.comonTitleScreen(
                    context: context,
                    title: "Session Details",
                  ),
                  // Container(
                  //   alignment: Alignment.center,
                  //   padding: EdgeInsets.all(10.0.h),
                  //   decoration: BoxDecoration(
                  //     border: Border.all(color: AppColors.greyColor),
                  //     borderRadius: BorderRadius.all(Radius.circular(24.0.r)),
                  //   ),
                  //   child: Row(
                  //     children: [
                  //       Icon(
                  //         Icons.calendar_month,
                  //         size: 16.0.h,
                  //         color: AppColors.blackColor,
                  //       ),
                  //       SizedBox(width: 10.0.w),
                  //       Text(
                  //         "Add to my agenda",
                  //         style: TextStyle(
                  //           fontSize: 12.0.sp,
                  //           color: AppColors.blackColor,
                  //         ),
                  //       ),
                  //     ],
                  //   ),
                  // ),
                ],
              ),
              SizedBox(height: 10.0.h),

              Expanded(
                child: ListView.separated(
                  separatorBuilder: (context, index) =>
                      SizedBox(height: 10.0.h),
                  itemCount: args.agendaDetails!.speakersList!.length,
                  itemBuilder: (context, index) => Container(
                    padding: EdgeInsets.all(10.0.h),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.greyColor),
                      borderRadius: BorderRadius.all(Radius.circular(16.0.r)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          args.agendaSpeakersDetails![index].speakingAt!,
                          style: TextStyle(
                            fontSize: 18.0.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.blackColor,
                          ),
                        ),
                        SizedBox(height: 10.0.h),
                        AgendaScreenDetailsWidgets.sessionWidgetFileds(
                          context: context,
                          image: AppImages.calendarAboutEventIcon,
                          title: "Date :",
                          subTitle: args.agendaDetails!.sessionDate!,
                        ),
                        SizedBox(height: 10.0.h),
                        AgendaScreenDetailsWidgets.sessionWidgetFileds(
                          context: context,
                          image: AppImages.clockIcon,
                          title: "Time :",
                          subTitle:
                              "${args.agendaDetails!.sessionStartTime} - ${args.agendaDetails!.sessionEndTime}",
                        ),
                        SizedBox(height: 10.0.h),
                        InkWell(
                          onTap: () {
                            Navigator.pushNamed(
                              context,
                              PATHS.agendaDetailsSpeakerScreen,
                              arguments: AgendaDetailsSpeakerScreen(
                                speakerId: args
                                    .agendaDetails!
                                    .speakersList![index]
                                    .speakerID,
                              ),
                            );
                          },
                          child:
                              AgendaScreenDetailsWidgets.showSpeakerSessionWidget(
                                context: context,
                                speakerData: args.agendaSpeakersDetails![index],
                              ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // SizedBox(height: 20.0.h),
              // ElevatedButton.icon(
              //   onPressed: () {},
              //   label: const Text("Chat"),
              //   icon: CommonComponents.imageAssetWithCache(
              //     context: context,
              //     image: AppImages.messageWhiteIcon,
              //     height: 24.0.h,
              //     width: 24.0.w,
              //     fit: BoxFit.contain,
              //   ),
              //   style: ElevatedButton.styleFrom(
              //     foregroundColor: Colors.white,
              //     backgroundColor: AppColors.midLevelGreenColor,
              //     minimumSize: Size(398.0.w, 48.0.h),
              //     textStyle: TextStyle(
              //       fontSize: 16.0.sp,
              //       fontWeight: FontWeight.bold,
              //     ),
              //   ),
              // ),
              // SizedBox(height: 20.0.h),
              // ElevatedButton.icon(
              //   onPressed: () async {

              //     // await CommonComponents.launchOnBrowser(
              //     //   context: context,
              //     //   url: args.agendaDetails!.shareLink!,
              //     // );
              //   },
              //   label: const Text("Share"),
              //   icon: CommonComponents.imageAssetWithCache(
              //     context: context,
              //     image: AppImages.shareRoundIcon,
              //     height: 24.0.h,
              //     width: 24.0.w,
              //     fit: BoxFit.contain,
              //   ),
              //   style: ElevatedButton.styleFrom(
              //     foregroundColor: Colors.white,
              //     backgroundColor: AppColors.darkGreenColor,
              //     minimumSize: Size(398.0.w, 48.0.h),
              //     textStyle: TextStyle(
              //       fontSize: 16.0.sp,
              //       fontWeight: FontWeight.bold,
              //     ),
              //   ),
              // ),
              SizedBox(height: 20.0.h),
              Center(
                child: ElevatedButton.icon(
                  onPressed: () async {
                    await AgendaScreenDetailsWidgets.showNoteAlertWidget(
                      context: context,
                      controller: _noteController,
                      formKey: _formKey,
                    );
                  },
                  label: const Text("Take a note"),
                  icon: CommonComponents.imageAssetWithCache(
                    context: context,
                    image: AppImages.sharePollIcon,
                    height: 24.0.h,
                    width: 24.0.w,
                    fit: BoxFit.contain,
                  ),
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: AppColors.midLevelGreenColor,
                    minimumSize: Size(398.0.w, 48.0.h),
                    textStyle: TextStyle(
                      fontSize: 16.0.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

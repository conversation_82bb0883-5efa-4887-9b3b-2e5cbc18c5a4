import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/models/home_screens_models/agenda_screens_models/agenda_details_speaker_details_model.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/agenda_screens_widgets/agenda_details_speaker_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class AgendaDetailsSpeakerScreen extends StatefulWidget {
  const AgendaDetailsSpeakerScreen({super.key, this.speakerId});
  final int? speakerId;

  @override
  State<AgendaDetailsSpeakerScreen> createState() =>
      _AgendaDetailsSpeakerScreenState();
}

class _AgendaDetailsSpeakerScreenState
    extends State<AgendaDetailsSpeakerScreen> {
  final TextEditingController _addNoteController = TextEditingController();
  final TextEditingController _addFirstMessageController =
      TextEditingController();
  final _formKey = GlobalKey<FormState>();
  Future<AgendaDetailsSpeakerDetailsModel>? _fetchSpeakerDetailsFromAgenda;
  bool callAPI = true;

  @override
  void didChangeDependencies() {
    final AgendaDetailsSpeakerScreen args =
        ModalRoute.of(context)!.settings.arguments
            as AgendaDetailsSpeakerScreen;

    if (callAPI) {
      _fetchSpeakerDetailsFromAgenda = context
          .read(HomeScreenCategoriesProviders.agendaScreenProvidersApis)
          .getSpeakerDetailsFromAgenda(
            context: context,
            speakerID: args.speakerId!,
          )
          .then((value) {
            callAPI = false;
            return value;
          });
    }

    super.didChangeDependencies();
  }

  @override
  void dispose() {
    _addNoteController.dispose();
    _addFirstMessageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final AgendaDetailsSpeakerScreen args =
        ModalRoute.of(context)!.settings.arguments
            as AgendaDetailsSpeakerScreen;
    return Scaffold(
      body: SafeArea(
        child: FutureBuilder(
          future: _fetchSpeakerDetailsFromAgenda,
          builder: (context, AsyncSnapshot<AgendaDetailsSpeakerDetailsModel> snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return CommonComponents.loadingDataFromServer();
            } else if (snapshot.data == null) {
              return Center(child: CommonComponents.noDataFoundWidget());
            } else {
              return SingleChildScrollView(
                padding: EdgeInsets.all(10.0.h),
                physics: const BouncingScrollPhysics(),
                child: Column(
                  children: [
                    Container(
                      padding: EdgeInsets.all(10.0.h),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.greyColor),
                        borderRadius: BorderRadius.all(Radius.circular(16.0.r)),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,

                                children: [
                                  ClipOval(
                                    child:
                                        CommonComponents.imageWithNetworkCache(
                                          context: context,
                                          image: snapshot.data!.speakerImage!,
                                          height: 40.0.h,
                                          width: 40.0.w,
                                          fit: BoxFit.contain,
                                        ),
                                  ),
                                  SizedBox(width: 5.0.w),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        snapshot.data!.speakerName!,
                                        style: TextStyle(
                                          fontSize: 14.0.sp,
                                          color: AppColors.blackColor,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      // SizedBox(height: 10.0.h),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 15.0.w,
                                          vertical: 7.0.h,
                                        ),
                                        alignment: Alignment.center,

                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.all(
                                            Radius.circular(16.0.r),
                                          ),
                                          color: AppColors.lightGreenColor,
                                        ),
                                        child: Text(
                                          snapshot.data!.categorySelect!,
                                          style: TextStyle(
                                            fontSize: 14.0.sp,
                                            color: AppColors.midLevelGreenColor,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              // Container(
                              //   alignment: Alignment.center,
                              //   padding: EdgeInsets.all(10.0.h),
                              //   decoration: const BoxDecoration(
                              //     shape: BoxShape.circle,
                              //     color: AppColors.lightgreyColor,
                              //   ),
                              //   child: Icon(
                              //     Icons.bookmark_border,
                              //     size: 15.0.h,
                              //     color: AppColors.midLevelGreenColor,
                              //   ),
                              // ),
                            ],
                          ),
                          SizedBox(height: 10.0.h),
                          AgendaDetailsSpeakerWidgets.sessionWidgetFileds(
                            context: context,
                            image: AppImages.jobIcon,
                            title: "Job :",
                            subTitle: snapshot.data!.job!,
                          ),
                          SizedBox(height: 10.0.h),
                          AgendaDetailsSpeakerWidgets.sessionWidgetFileds(
                            context: context,
                            image: AppImages.companyIcon,
                            title: "Company:",
                            subTitle: snapshot.data!.company!,
                          ),
                          SizedBox(height: 10.0.h),
                          AgendaDetailsSpeakerWidgets.sessionWidgetFileds(
                            context: context,
                            image: AppImages.countryIcon,
                            title: "Country :",
                            subTitle: snapshot.data!.country!,
                          ),
                          SizedBox(height: 20.0.h),
                          const Divider(color: AppColors.greyColor),
                          Row(
                            children: [
                              AgendaDetailsSpeakerWidgets.sessionButtonsWidget(
                                context: context,
                                image: AppImages.noteIcon,
                                onPress: () async {
                                  await AgendaDetailsSpeakerWidgets.showNoteAlertWidget(
                                    context: context,
                                    controller: _addNoteController,
                                    formKey: _formKey,
                                  );
                                },
                              ),
                              SizedBox(width: 10.0.w),
                              AgendaDetailsSpeakerWidgets.sessionButtonsWidget(
                                context: context,
                                image: AppImages.messageIcon,
                                onPress: () async {
                                  await AgendaDetailsSpeakerWidgets.showSendNewMessageAlertWidget(
                                    context: context,
                                    controller: _addFirstMessageController,
                                    formKey: _formKey,
                                    speakerID: args.speakerId!,
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 10.0.h),
                    Container(
                      padding: EdgeInsets.all(10.0.h),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.greyColor),
                        borderRadius: BorderRadius.all(Radius.circular(24.0.r)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              CommonComponents.imageAssetWithCache(
                                context: context,
                                image: AppImages.aboutIcon,
                                height: 16.0.h,
                                width: 16.0.w,
                                fit: BoxFit.contain,
                              ),
                              SizedBox(width: 5.0.w),
                              Text(
                                "About :",
                                style: TextStyle(
                                  fontSize: 12.0.sp,
                                  color: AppColors.greyColor,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10.0.h),
                          Text(
                            snapshot.data!.about!,
                            style: TextStyle(
                              fontSize: 10.0.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textColor,
                            ),
                          ),
                          SizedBox(height: 10.0.h),
                          const Divider(color: AppColors.greyColor),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              CommonComponents.imageAssetWithCache(
                                context: context,
                                image: AppImages.articeIcon,
                                height: 16.0.h,
                                width: 16.0.w,
                                fit: BoxFit.contain,
                              ),
                              SizedBox(width: 5.0.w),
                              Text(
                                "Speaking at :",
                                style: TextStyle(
                                  fontSize: 12.0.sp,
                                  color: AppColors.greyColor,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10.0.h),
                          Text(
                            snapshot.data!.speakingAt!,
                            style: TextStyle(
                              fontSize: 10.0.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textColor,
                            ),
                          ),
                          SizedBox(height: 10.0.h),
                          const Divider(color: AppColors.greyColor),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              CommonComponents.imageAssetWithCache(
                                context: context,
                                image: AppImages.expierenceIcon,
                                height: 16.0.h,
                                width: 16.0.w,
                                fit: BoxFit.contain,
                              ),
                              SizedBox(width: 5.0.w),
                              Text(
                                "Experience :",
                                style: TextStyle(
                                  fontSize: 12.0.sp,
                                  color: AppColors.greyColor,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10.0.h),

                          ListView.separated(
                            shrinkWrap: true,
                            separatorBuilder: (context, index) =>
                                SizedBox(height: 5.0.h),
                            itemCount: snapshot.data!.experience!.length,
                            itemBuilder: (context, index) => Row(
                              children: [
                                CommonComponents.imageWithNetworkCache(
                                  context: context,
                                  image:
                                      snapshot
                                          .data!
                                          .experience![index]
                                          .image! ??
                                      CommonComponents.imageNotFound,
                                  height: 24.0.h,
                                  width: 24.0.w,
                                  fit: BoxFit.contain,
                                ),
                                SizedBox(width: 5.0.w),
                                Text(
                                  snapshot.data!.experience![index].name!
                                      .toString(),
                                  style: TextStyle(
                                    fontSize: 10.0.sp,
                                    color: AppColors.blackColor,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const Divider(color: AppColors.greyColor),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              CommonComponents.imageAssetWithCache(
                                context: context,
                                image: AppImages.linksIcon,
                                height: 16.0.h,
                                width: 16.0.w,
                                fit: BoxFit.contain,
                              ),
                              SizedBox(width: 5.0.w),
                              Text(
                                "Links :",
                                style: TextStyle(
                                  fontSize: 12.0.sp,
                                  color: AppColors.greyColor,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10.0.h),
                          ListView.separated(
                            shrinkWrap: true,
                            separatorBuilder: (context, index) =>
                                SizedBox(height: 10.0.h),
                            itemCount: snapshot.data!.socialMediaLinks!.length,
                            itemBuilder: (context, index) => Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  snapshot.data!.socialMediaLinks![index].name!,
                                  style: TextStyle(fontSize: 10.0.sp),
                                ),
                                InkWell(
                                  onTap: () async {
                                    await CommonComponents.launchOnBrowser(
                                      context: context,
                                      url: snapshot
                                          .data!
                                          .socialMediaLinks![index]
                                          .link!,
                                    );
                                  },
                                  child: CommonComponents.imageAssetWithCache(
                                    context: context,
                                    image: AppImages.shareIcon,
                                    height: 20.0.h,
                                    width: 20.0.w,
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }
          },
        ),
      ),
    );
  }
}

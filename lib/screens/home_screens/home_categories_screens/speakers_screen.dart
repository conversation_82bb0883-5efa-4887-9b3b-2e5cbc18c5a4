import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:afa_app/models/home_screens_models/speakers_screens_models/speeaker_screen_model.dart';
import 'package:afa_app/screens/home_screens/home_categories_screens/speakers_screens/speaker_details_screen.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/speakers_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class SpeakersScreen extends StatefulWidget {
  const SpeakersScreen({super.key});

  @override
  State<SpeakersScreen> createState() => _SpeakersScreenState();
}

class _SpeakersScreenState extends State<SpeakersScreen> {
  final TextEditingController _addNoteController = TextEditingController();
  final TextEditingController _addNewMessageController =
      TextEditingController();
  final _formKey = GlobalKey<FormState>();
  Future<List<SpeakerScreenModel>>? _fetchSpeakersList;

  @override
  void initState() {
    _fetchSpeakersList = context
        .read(HomeScreenCategoriesProviders.speakersScreenProvidersApis)
        .getSpeakersList(context: context);
    super.initState();
  }

  @override
  void dispose() {
    _addNoteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: FutureBuilder(
          future: _fetchSpeakersList,
          builder: (context, AsyncSnapshot<List<SpeakerScreenModel>> snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(child: CommonComponents.loadingDataFromServer());
            } else if (snapshot.data == null) {
              return Center(child: CommonComponents.noDataFoundWidget());
            } else {
              return Padding(
                padding: EdgeInsets.all(10.0.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonComponents.comonTitleScreen(
                      context: context,
                      title: "Speakers",
                    ),
                    SizedBox(height: 10.0.h),
                    Expanded(
                      child: ListView.separated(
                        physics: const BouncingScrollPhysics(),
                        separatorBuilder: (context, index) =>
                            SizedBox(height: 10.0.h),
                        itemCount: snapshot.data!.length,
                        itemBuilder: (context, index) => Container(
                          padding: EdgeInsets.all(10.0.h),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.greyColor),
                            borderRadius: BorderRadius.all(
                              Radius.circular(16.0.r),
                            ),
                          ),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,

                                    children: [
                                      ClipOval(
                                        child:
                                            CommonComponents.imageWithNetworkCache(
                                              context: context,
                                              image:
                                                  snapshot.data![index].image!,
                                              height: 40.0.h,
                                              width: 40.0.w,
                                              fit: BoxFit.contain,
                                            ),
                                      ),
                                      SizedBox(width: 5.0.w),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            snapshot.data![index].name!,
                                            style: TextStyle(
                                              fontSize: 14.0.sp,
                                              color: AppColors.blackColor,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          // SizedBox(height: 10.0.h),
                                          Container(
                                            padding: EdgeInsets.symmetric(
                                              horizontal: 15.0.w,
                                              vertical: 7.0.h,
                                            ),
                                            alignment: Alignment.center,

                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.all(
                                                Radius.circular(16.0.r),
                                              ),
                                              color: AppColors.lightGreenColor,
                                            ),
                                            child: Text(
                                              snapshot.data![index].category!,
                                              style: TextStyle(
                                                fontSize: 14.0.sp,
                                                color: AppColors
                                                    .midLevelGreenColor,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  Container(
                                    alignment: Alignment.center,
                                    padding: EdgeInsets.all(10.0.h),
                                    decoration: const BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: AppColors.lightgreyColor,
                                    ),
                                    child: Icon(
                                      Icons.bookmark_border,
                                      size: 15.0.h,
                                      color: AppColors.midLevelGreenColor,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 10.0.h),
                              SpeakersScreenWidgets.sessionWidgetFileds(
                                context: context,
                                image: AppImages.jobIcon,
                                title: "Job :",
                                subTitle: snapshot.data![index].jobTitle!,
                              ),
                              SizedBox(height: 10.0.h),
                              SpeakersScreenWidgets.sessionWidgetFileds(
                                context: context,
                                image: AppImages.companyIcon,
                                title: "Company:",
                                subTitle: snapshot.data![index].companyName!,
                              ),
                              SizedBox(height: 10.0.h),
                              SpeakersScreenWidgets.sessionWidgetFileds(
                                context: context,
                                image: AppImages.countryIcon,
                                title: "Country :",
                                subTitle: snapshot.data![index].country!,
                              ),
                              SizedBox(height: 20.0.h),
                              const Divider(color: AppColors.greyColor),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      SpeakersScreenWidgets.sessionButtonsWidget(
                                        context: context,
                                        image: AppImages.noteIcon,
                                        onPress: () async {
                                          await SpeakersScreenWidgets.showNoteAlertWidget(
                                            context: context,
                                            controller: _addNoteController,
                                            formKey: _formKey,
                                          );
                                        },
                                      ),
                                      SizedBox(width: 10.0.w),
                                      SpeakersScreenWidgets.sessionButtonsWidget(
                                        context: context,
                                        image: AppImages.messageIcon,
                                        onPress: () async {
                                          await SpeakersScreenWidgets.showSendNewMessageAlertWidget(
                                            context: context,
                                            controller:
                                                _addNewMessageController,
                                            formKey: _formKey,
                                            speakerID: int.parse(
                                              snapshot.data![index].speakerID!,
                                            ),
                                          );
                                        },
                                      ),
                                      SizedBox(width: 10.0.w),
                                      SpeakersScreenWidgets.sessionButtonsWidget(
                                        context: context,
                                        image: AppImages.videoIcon,
                                        onPress: () {
                                          Navigator.pushNamed(
                                            context,
                                            PATHS.requestMeetingScreenSpeaker,
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      Navigator.pushNamed(
                                        context,
                                        PATHS.speakerDetailsScreen,
                                        arguments: SpeakerDetailsScreen(
                                          speakerDetails: snapshot.data![index],
                                        ),
                                      );
                                    },
                                    style: TextButton.styleFrom(
                                      foregroundColor:
                                          AppColors.midLevelGreenColor,
                                      padding: const EdgeInsets.all(0.0),
                                      tapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                      minimumSize: Size(27.0.w, 17.0.h),
                                      textStyle: TextStyle(
                                        fontSize: 12.0.sp,
                                        fontWeight: FontWeight.bold,
                                        decoration: TextDecoration.underline,
                                        height: 1.0.h,
                                      ),
                                    ),
                                    child: const Text("View"),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }
          },
        ),
      ),
    );
  }
}

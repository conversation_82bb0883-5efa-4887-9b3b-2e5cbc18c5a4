import 'package:afa_app/app_config/api_providers/chat_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:afa_app/models/chat_models/inbox_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class InboxScreen extends StatefulWidget {
  const InboxScreen({super.key});

  @override
  State<InboxScreen> createState() => _InboxScreenState();
}

class _InboxScreenState extends State<InboxScreen> {
  Future<List<InboxModel>>? _fetchChatInbox;

  @override
  void initState() {
    _fetchChatInbox = context
        .read(ChatProviders.inboxProvidersApis)
        .getChatInbox(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: _fetchChatInbox,
      builder: (context, AsyncSnapshot<List<InboxModel>> snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(child: CommonComponents.loadingDataFromServer());
        } else if (snapshot.data == null) {
          return Center(child: CommonComponents.noDataFoundWidget());
        } else {
          return ListView.separated(
            physics: const BouncingScrollPhysics(),
            shrinkWrap: true,
            separatorBuilder: (context, index) =>
                const Divider(color: AppColors.greyColor),
            itemCount: snapshot.data!.length,
            itemBuilder: (context, index) => InkWell(
              onTap: () {
                context
                    .read(ChatProviders.inboxProvidersApis)
                    .setThreadID(snapshot.data![index].threadId!);
                // print(context.read(ChatProviders.inboxProvidersApis).threadID);

                Navigator.pushNamed(context, PATHS.chatScreen);
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      ClipOval(
                        child: CommonComponents.imageWithNetworkCache(
                          context: context,
                          image: snapshot.data![index].senderUserImage!,
                          height: 40.0.h,
                          width: 40.0.w,
                          fit: BoxFit.contain,
                        ),
                      ),
                      SizedBox(width: 5.0.w),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            snapshot.data![index].senderUserName!,
                            style: TextStyle(
                              fontSize: 14.0.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  Visibility(
                    visible: snapshot.data![index].unreadCount != 0,
                    child: Container(
                      height: 24.0.h,
                      width: 24.0.w,
                      alignment: Alignment.center,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.red,
                      ),
                      child: Text(
                        snapshot.data![index].unreadCount.toString(),
                        style: TextStyle(
                          fontSize: 10.0.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }
      },
    );
  }
}

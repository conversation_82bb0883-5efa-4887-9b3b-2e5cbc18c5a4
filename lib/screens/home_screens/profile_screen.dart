import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:afa_app/widgets/home_screens_widgets.dart/profile_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  String? userImage, userName, userPosition;

  @override
  void initState() {
    Future.delayed(Duration.zero, () async {
      final String userImageCached = await CommonComponents.getSavedData(
        ApiKeys.userImage,
      );
      final String userNameCached = await CommonComponents.getSavedData(
        ApiKeys.userName,
      );
      final String userPositionCached = await CommonComponents.getSavedData(
        ApiKeys.userPosition,
      );
      setState(() {
        userName = userNameCached;
        userImage = userImageCached;
        userPosition = userPositionCached;
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (userImage == null || userName == null || userPosition == null) {
      return Center(child: CommonComponents.loadingDataFromServer());
    }

    return Scaffold(
      backgroundColor: AppColors.lightgreyColor,
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          padding: EdgeInsets.all(10.0.h),

          child: Consumer(
            builder: (context, watch, child) => Column(
              // key: watch
              //     .watch(ProfileProviders.profileSettingsProvidersApis)
              //     .profileKey,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CommonComponents.comonTitleScreen(
                      context: context,
                      title: "Your Account",
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.pushNamed(context, PATHS.settingScreen);
                      },
                      child: Container(
                        alignment: Alignment.center,
                        padding: EdgeInsets.all(10.0.h),
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white,
                        ),
                        child: Icon(
                          Icons.settings,
                          color: Colors.black,
                          size: 24.0.h,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 20.0.h),
                Center(
                  child: ClipOval(
                    child: CommonComponents.imageWithNetworkCache(
                      context: context,
                      image: userImage!,
                      height: 104.0.h,
                      width: 104.0.w,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
                SizedBox(height: 10.0.h),
                Center(
                  child: Text(
                    userName!,
                    style: TextStyle(
                      fontSize: 20.0.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.blackColor,
                    ),
                  ),
                ),
                Center(
                  child: Text(
                    userPosition!,
                    style: TextStyle(
                      fontSize: 16.0.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.greyColor,
                    ),
                  ),
                ),
                SizedBox(height: 20.0.h),
                Container(
                  padding: EdgeInsets.all(15.0.h),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(24.0.r)),
                    color: Colors.white,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Account Information",
                        style: TextStyle(
                          fontSize: 14.0.sp,
                          color: AppColors.greyColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 10.0.h),
                      ProfileScreenWidgets.profileFileds(
                        context: context,
                        image: AppImages.profileIcon,
                        title: "Personal Information",
                        onPress: () {
                          Navigator.pushNamed(
                            context,
                            PATHS.personalInformationScreen,
                          );
                        },
                      ),
                      SizedBox(height: 10.0.h),
                      ProfileScreenWidgets.profileFileds(
                        context: context,
                        image: AppImages.badgeIcon,
                        title: "Your Badge",
                        onPress: () {
                          Navigator.pushNamed(context, PATHS.badgeScreen);
                        },
                      ),
                      SizedBox(height: 20.0.h),
                      Text(
                        "Resources",
                        style: TextStyle(
                          fontSize: 14.0.sp,
                          color: AppColors.greyColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 10.0.h),

                      ProfileScreenWidgets.profileFileds(
                        context: context,
                        image: AppImages.photosIcon,
                        title: "Photos",
                        onPress: () {
                          Navigator.pushNamed(context, PATHS.photosScreen);
                        },
                      ),
                      SizedBox(height: 10.0.h),
                      ProfileScreenWidgets.profileFileds(
                        context: context,
                        image: AppImages.noteIcon,
                        title: "Notes",
                        onPress: () {
                          Navigator.pushNamed(context, PATHS.notesScreen);
                        },
                      ),

                      SizedBox(height: 10.0.h),
                      ProfileScreenWidgets.profileFileds(
                        context: context,
                        image: AppImages.changePasswordIcon,
                        title: "Change Password",
                        onPress: () {
                          Navigator.pushNamed(
                            context,
                            PATHS.resetPasswordScreen,
                          );
                        },
                      ),

                      SizedBox(height: 10.0.h),
                      ProfileScreenWidgets.profileFileds(
                        context: context,
                        image: AppImages.logoutIcon,
                        title: "Logout",
                        onPress: () async {
                          await ProfileScreenWidgets.logOutAlert(
                            context: context,
                          );
                        },
                        textColor: Colors.red,
                        filedColor: const Color(0XFFFEE1DE),
                        arrowColor: Colors.red,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

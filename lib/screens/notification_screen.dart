import 'package:afa_app/app_config/api_providers/home_screen_categories_providers.dart';
import 'package:afa_app/app_config/api_providers/profile_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:afa_app/models/notifications_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  Future<List<NotificationsModel>>? _fetchAllNotifications;

  @override
  void initState() {
    _fetchAllNotifications = context
        .read(HomeScreenCategoriesProviders.notificationsScreenProvidersApis)
        .getAllNotifications(context: context);

    Future.delayed(Duration.zero, () async {
      if (!mounted) return;
      await context
          .read(HomeScreenCategoriesProviders.notificationsScreenProvidersApis)
          .readNotifiation(context: context);
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            CommonComponents.comonTitleScreen(
              context: context,
              title: "Notifications",
            ),

            FutureBuilder(
              future: _fetchAllNotifications,
              builder: (context, AsyncSnapshot<List<NotificationsModel>> snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(
                    child: CommonComponents.loadingDataFromServer(),
                  );
                } else if (snapshot.data == null) {
                  return Center(child: CommonComponents.noDataFoundWidget());
                } else {
                  return Expanded(
                    child: ListView.separated(
                      padding: EdgeInsets.all(10.0.h),
                      physics: const BouncingScrollPhysics(),
                      separatorBuilder: (context, index) =>
                          SizedBox(height: 10.0.h),
                      itemCount: snapshot.data!.length,
                      itemBuilder: (context, index) => InkWell(
                        onTap: () async {
                          // await context.read(HomeScreenCategoriesProviders.notificationsScreenProvidersApis).readNotifiation(context: context, notificationID: snapshot.data[index].);
                          // ;
                          if (snapshot.data![index].type == "announcement") {
                            context
                                .read(ProfileProviders.commonApis)
                                .setCurrentIndex(1);
                            Navigator.pop(context);
                          } else if (snapshot.data![index].type == "Messages") {
                            context
                                .read(ProfileProviders.commonApis)
                                .setCurrentIndex(3);
                            Navigator.pop(context);
                          } else if (snapshot.data![index].type == "Comment") {
                            Navigator.pushNamed(
                              context,
                              PATHS.sessionQuestionsScreen,
                            );
                          }
                        },
                        child: Container(
                          padding: EdgeInsets.all(10.0.h),

                          width: 398.0.w,
                          decoration: BoxDecoration(
                            color: snapshot.data![index].read!
                                ? null
                                : AppColors.lightGreenColor,
                            border: Border.all(
                              color: AppColors.lightGreenColor,
                            ),
                            borderRadius: BorderRadius.all(
                              Radius.circular(16.0.r),
                            ),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,

                            children: [
                              Container(
                                padding: EdgeInsets.all(10.0.h),
                                decoration: const BoxDecoration(
                                  color: AppColors.lightGreenColor,
                                  shape: BoxShape.circle,
                                ),
                                child: CommonComponents.imageAssetWithCache(
                                  context: context,
                                  image: AppImages.announcementActiveIcon,
                                  height: 24.0.h,
                                  width: 24.0.w,
                                  fit: BoxFit.contain,
                                ),
                              ),
                              SizedBox(width: 10.0.w),

                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      snapshot.data![index].type!,
                                      style: TextStyle(
                                        fontSize: 14.0.sp,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.greyColor,
                                      ),
                                    ),
                                    SizedBox(height: 10.0.h),
                                    Text(
                                      snapshot.data![index].title!,
                                      style: TextStyle(
                                        fontSize: 16.0.sp,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.blackColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:afa_app/app_config/api_providers/athuntication_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/widgets/athuntication_screens_widgets/reset_password_screen_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class ResetPasswordScreen extends StatefulWidget {
  const ResetPasswordScreen({super.key});

  @override
  State<ResetPasswordScreen> createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> {
  final TextEditingController _emailController = TextEditingController();
  final _formkey = GlobalKey<FormState>();

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(15.0.h),
          physics: const BouncingScrollPhysics(),
          child: Form(
            key: _formkey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CommonComponents.comonTitleScreen(
                  context: context,
                  title: "Reset Password",
                ),
                SizedBox(height: 20.0.h),
                Text(
                  "Reset Password",
                  style: TextStyle(
                    fontSize: 22.0.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 5.0.h),
                Text(
                  "Please enter your email address to receive a verification PIN.",
                  style: TextStyle(
                    fontSize: 18.0.sp,
                    color: AppColors.greyColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 10.0.h),
                Text(
                  "Email",
                  style: TextStyle(
                    fontSize: 14.0.sp,
                    color: AppColors.textColor,
                  ),
                ),
                SizedBox(height: 5.0.h),
                ResetPasswordScreenWidgets.resetPasswordScreenFileds(
                  controller: _emailController,
                  type: TextInputType.emailAddress,
                  action: TextInputAction.done,
                  hint: "Enter",
                  validate: "Plaese Enter Your Email Address",
                ),
                SizedBox(height: 25.0.h),
                ElevatedButton(
                  onPressed: () async {
                    if (_formkey.currentState!.validate()) {
                      await context
                          .read(
                            AthunticationProviders.resetPasswordProvidersApis,
                          )
                          .resetPassword(
                            context: context,
                            email: _emailController.text,
                          );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: AppColors.midLevelGreenColor,
                    textStyle: TextStyle(
                      fontSize: 16.0.sp,
                      fontWeight: FontWeight.bold,
                    ),
                    minimumSize: Size(398.0.w, 46.0.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(Radius.circular(32.0.r)),
                    ),
                  ),
                  child: const Text("Next"),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// import 'package:afa_app/app_config/app_colors.dart';
// import 'package:afa_app/app_config/common_components.dart';
// import 'package:afa_app/app_config/routes.dart';
// import 'package:afa_app/widgets/athuntication_screens_widgets/set_new_password_screen_widgets.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';

// class SetNewPasswordScreen extends StatefulWidget {
//   const SetNewPasswordScreen({super.key});

//   @override
//   State<SetNewPasswordScreen> createState() => _SetNewPasswordScreenState();
// }

// class _SetNewPasswordScreenState extends State<SetNewPasswordScreen> {
//   final TextEditingController _emailController = TextEditingController();
//   final TextEditingController _passwordController = TextEditingController();
//   final _formkey = GlobalKey<FormState>();

//   @override
//   void dispose() {
//     _emailController.dispose();
//     _passwordController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: SafeArea(
//         child: SingleChildScrollView(
//           physics: const BouncingScrollPhysics(),
//           padding: EdgeInsets.all(15.0.h),
//           child: Form(
//             key: _formkey,
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 CommonComponents.comonTitleScreen(
//                   context: context,
//                   title: "New Password",
//                 ),
//                 SizedBox(height: 20.0.h),
//                 Text(
//                   "New Password",
//                   style: TextStyle(
//                     fontSize: 22.0.sp,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//                 SizedBox(height: 5.0.h),
//                 Text(
//                   "Set a New Password",
//                   style: TextStyle(
//                     fontSize: 18.0.sp,
//                     color: AppColors.greyColor,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//                 SizedBox(height: 10.0.h),
//                 SetNewPasswordScreenWidgets.setNewPassworScreenFileds(
//                   controller: _emailController,
//                   type: TextInputType.emailAddress,
//                   action: TextInputAction.next,
//                   hint: "Enter",
//                   validate: "Plaese Enter Your Email Address",
//                 ),
//                 SizedBox(height: 10.0.h),
//                 Text(
//                   "Password",
//                   style: TextStyle(
//                     fontSize: 14.0.sp,
//                     color: AppColors.textColor,
//                   ),
//                 ),
//                 SizedBox(height: 5.0.h),
//                 SetNewPasswordScreenWidgets.setNewPassworScreenFileds(
//                   controller: _passwordController,
//                   type: TextInputType.visiblePassword,
//                   action: TextInputAction.done,
//                   hint: "Enter",
//                   validate: "Plaese Enter Your Password",
//                   showPasswordIcon: true,
//                 ),
//                 SizedBox(height: 15.0.h),
//                 ElevatedButton(
//                   onPressed: () {
//                     if (_formkey.currentState!.validate()) {
//                       Navigator.pushNamed(context, PATHS.mainScreen);
//                     }
//                   },
//                   style: ElevatedButton.styleFrom(
//                     foregroundColor: Colors.white,
//                     backgroundColor: AppColors.midLevelGreenColor,
//                     textStyle: TextStyle(
//                       fontSize: 16.0.sp,
//                       fontWeight: FontWeight.bold,
//                     ),
//                     minimumSize: Size(398.0.w, 46.0.h),
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.all(Radius.circular(32.0.r)),
//                     ),
//                   ),
//                   child: const Text("Login"),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

import 'dart:async';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/common_components.dart';
// import 'package:afa_app/app_config/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class OtpScreen extends StatefulWidget {
  const OtpScreen({super.key});

  @override
  State<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends State<OtpScreen> {
  final TextEditingController _otpController = TextEditingController();
  int secondRemaining = 15;
  bool enableResend = false;
  Timer? _timer;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    startTime();
    super.initState();
  }

  void startTime() {
    _timer?.cancel();
    secondRemaining = 15;
    setState(() {
      enableResend = false;
    });
    Timer.periodic(const Duration(seconds: 1), (t) {
      if (secondRemaining == 0) {
        setState(() {
          enableResend = true;
          t.cancel();
        });
      } else {
        setState(() {
          secondRemaining--;
        });
      }
    });
  }

  void resendCode() {
    startTime();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          padding: EdgeInsets.all(15.0.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonComponents.comonTitleScreen(context: context, title: "OTP"),
              SizedBox(height: 20.0.h),
              Text(
                "OTP",
                style: TextStyle(
                  fontSize: 22.0.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 5.0.h),
              Text.rich(
                TextSpan(
                  text: "We have just sent you 6 digit code via your email ",
                  style: TextStyle(
                    fontSize: 18.0.sp,
                    color: AppColors.greyColor,
                    fontWeight: FontWeight.bold,
                  ),
                  children: [
                    TextSpan(
                      text: "<EMAIL>",
                      style: TextStyle(
                        fontSize: 18.0.sp,
                        color: AppColors.midLevelGreenColor,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 10.0.h),
              Form(
                key: _formKey,
                child: PinCodeTextField(
                  controller: _otpController,
                  validator: (value) =>
                      value!.isEmpty ? "Please Enter Your OTP" : null,
                  appContext: context,
                  length: 6,
                  enableActiveFill: true,
                  textStyle: TextStyle(fontSize: 20.0.sp, color: Colors.white),
                  keyboardType: TextInputType.number,
                  autovalidateMode: AutovalidateMode.onUserInteraction,

                  pinTheme: PinTheme(
                    inactiveColor: AppColors.greyColor,
                    selectedColor: AppColors.midLevelGreenColor,
                    selectedFillColor: AppColors.midLevelGreenColor,
                    inactiveFillColor: AppColors.greyColor,
                    shape: PinCodeFieldShape.circle,
                  ),
                ),
              ),
              Text(
                "00:${secondRemaining.toString()}",
                style: TextStyle(
                  fontSize: 16.0.sp,
                  color: AppColors.midLevelGreenColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Visibility(
                visible: enableResend,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      "Don’t Resend Any Code? ",
                      style: TextStyle(
                        fontSize: 16.0.sp,
                        color: AppColors.greyColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        startTime();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: AppColors.midLevelGreenColor,
                        textStyle: TextStyle(
                          fontSize: 16.0.sp,
                          fontWeight: FontWeight.bold,
                        ),
                        padding: const EdgeInsets.all(0.0),
                      ),
                      child: const Text("Resend"),
                    ),
                  ],
                ),
              ),

              SizedBox(height: 25.0.h),
              ElevatedButton(
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    // Navigator.pushNamed(context, PATHS.setNewPasswordScreen);
                  }
                },
                style: ElevatedButton.styleFrom(
                  foregroundColor: Colors.white,
                  backgroundColor: AppColors.midLevelGreenColor,
                  textStyle: TextStyle(
                    fontSize: 16.0.sp,
                    fontWeight: FontWeight.bold,
                  ),
                  minimumSize: Size(398.0.w, 46.0.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(32.0.r)),
                  ),
                ),
                child: const Text("Next"),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

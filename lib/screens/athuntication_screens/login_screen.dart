import 'package:afa_app/app_config/api_keys.dart';
import 'package:afa_app/app_config/api_providers/athuntication_providers.dart';
import 'package:afa_app/app_config/app_colors.dart';
import 'package:afa_app/app_config/app_images.dart';
import 'package:afa_app/app_config/common_components.dart';
import 'package:afa_app/app_config/routes.dart';
import 'package:afa_app/widgets/athuntication_screens_widgets/login_screen_widgets.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:riverpod_context/riverpod_context.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => LoginScreenState();
}

class LoginScreenState extends State<LoginScreen> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final _formkey = GlobalKey<FormState>();

  @override
  void initState() {
    Future.delayed(Duration.zero, () async {
      if (await CommonComponents.getSavedData(ApiKeys.userToken) != null) {
        if (!mounted) return;

        Navigator.pushNamedAndRemoveUntil(
          context,
          PATHS.mainScreen,
          (route) => false,
        );
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // final StateProvider<bool> checkRememberMe = StateProvider((ref) => false);
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          padding: EdgeInsets.all(20.0.h),
          child: Form(
            key: _formkey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,

              children: [
                Center(
                  child: CommonComponents.imageAssetWithCache(
                    context: context,
                    image: AppImages.loginlogoImage,
                    height: 93.0.h,
                    width: 247.0.w,
                    fit: BoxFit.contain,
                  ),
                ),
                SizedBox(height: 15.0.h),
                Text(
                  "Welcome Back",
                  style: TextStyle(
                    fontSize: 22.0.sp,
                    color: AppColors.blackColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),

                Text(
                  "Get Ready to Dive Back into AFA Conference",
                  style: TextStyle(
                    fontSize: 18.0.sp,
                    color: AppColors.greyColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 20.0.h),
                Text(
                  "Email",
                  style: TextStyle(
                    fontSize: 14.0.sp,
                    color: AppColors.textColor,
                  ),
                ),
                SizedBox(height: 5.0.h),
                LoginScreenWidgets.loginScreenFileds(
                  controller: _emailController,
                  type: TextInputType.emailAddress,
                  action: TextInputAction.next,
                  hint: "Enter",
                  validate: "Plaese Enter Your Email Address",
                ),
                SizedBox(height: 10.0.h),
                Text(
                  "Password",
                  style: TextStyle(
                    fontSize: 14.0.sp,
                    color: AppColors.textColor,
                  ),
                ),
                SizedBox(height: 5.0.h),
                LoginScreenWidgets.loginScreenFileds(
                  controller: _passwordController,
                  type: TextInputType.visiblePassword,
                  action: TextInputAction.done,
                  hint: "Enter",
                  validate: "Plaese Enter Your Password",
                  showPasswordIcon: true,
                ),
                SizedBox(height: 10.0.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Consumer(
                    //   builder: (context, watch, child) => Row(
                    //     children: [
                    //       Checkbox(
                    //         value: watch.watch(checkRememberMe),
                    //         checkColor: Colors.white,
                    //         activeColor: AppColors.midLevelGreenColor,
                    //         onChanged: (value) {
                    //           context.read(checkRememberMe.notifier).state =
                    //               !context.read(checkRememberMe.notifier).state;
                    //         },
                    //       ),

                    //       Text(
                    //         "Remember Me",
                    //         style: TextStyle(
                    //           fontSize: 16.0.sp,
                    //           color: AppColors.textColor,
                    //           fontWeight: FontWeight.bold,
                    //         ),
                    //       ),
                    //     ],
                    //   ),
                    // ),
                    TextButton(
                      onPressed: () {
                        Navigator.pushNamed(context, PATHS.resetPasswordScreen);
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: AppColors.midLevelGreenColor,
                        textStyle: TextStyle(
                          fontSize: 16.0.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      child: const Text("Forgot Password?"),
                    ),
                  ],
                ),
                SizedBox(height: 15.0.h),
                Consumer(
                  builder: (context, watch, child) => ElevatedButton(
                    onPressed: () async {
                      if (_formkey.currentState!.validate()) {
                        FirebaseMessaging.instance.getToken().then((
                          value,
                        ) async {
                          if (!context.mounted) return;
                          await context
                              .read(AthunticationProviders.loginProvidersApis)
                              .userLogin(
                                context: context,
                                email: _emailController.text,
                                password: _passwordController.text,
                                fcmToken: value!,
                                // userSelectedRememberMe: watch.watch(
                                //   checkRememberMe,
                                // ),
                              );
                        });
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Colors.white,
                      backgroundColor: AppColors.midLevelGreenColor,
                      textStyle: TextStyle(
                        fontSize: 16.0.sp,
                        fontWeight: FontWeight.bold,
                      ),
                      minimumSize: Size(398.0.w, 46.0.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.all(Radius.circular(32.0.r)),
                      ),
                    ),
                    child: const Text("Login"),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
